// 页面栈store
import { defineStore } from "pinia"

// 页面栈项接口定义
interface RouteItem {
  url: string
  timestamp: number
  params?: Record<string, any>
}

// 页面栈状态接口定义
interface RouteHistoryState {
  routeHistory: RouteItem[]
}

export const useRouteHistoryStore = defineStore("ROUTE_HISTORY", {
  state: (): RouteHistoryState => ({
    // 从本地存储中恢复页面栈，确保H5刷新后不丢失
    routeHistory: (() => {
      try {
        const stored = uni.getStorageSync("routeHistory")
        return stored ? JSON.parse(stored) : []
      } catch (error) {
        console.warn("Failed to parse route history from storage:", error)
        return []
      }
    })()
  }),

  actions: {
    // 保存页面栈到本地存储
    _saveToStorage() {
      try {
        uni.setStorageSync("routeHistory", JSON.stringify(this.routeHistory))
      } catch (error) {
        console.error("Failed to save route history to storage:", error)
      }
    },

    // 添加页面到栈中（内部方法）
    _pushToHistory(url: string, params?: Record<string, any>) {
      const routeItem: RouteItem = {
        url,
        timestamp: Date.now(),
        params
      }

      // 避免重复添加相同页面
      const lastRoute = this.routeHistory[this.routeHistory.length - 1]
      if (!lastRoute || lastRoute.url !== url) {
        this.routeHistory.push(routeItem)
        this._saveToStorage()
      }
    },

    // 从栈中移除页面（内部方法）
    _popFromHistory(count: number = 1) {
      for (let i = 0; i < count && this.routeHistory.length > 0; i++) {
        this.routeHistory.pop()
      }
      this._saveToStorage()
    },

    // 导航到新页面（保留当前页面）
    navigateTo(url: string, params?: Record<string, any>) {
      return new Promise((resolve, reject) => {
        const options: UniApp.NavigateToOptions = {
          url: params ? `${url}?${this._buildQuery(params)}` : url,
          success: res => {
            this._pushToHistory(url, params)
            resolve(res)
          },
          fail: err => {
            console.error("navigateTo failed:", err)
            reject(err)
          }
        }
        uni.navigateTo(options)
      })
    },

    // 关闭当前页面，跳转到应用内的某个页面
    redirectTo(url: string, params?: Record<string, any>) {
      return new Promise((resolve, reject) => {
        const options: UniApp.RedirectToOptions = {
          url: params ? `${url}?${this._buildQuery(params)}` : url,
          success: res => {
            // redirectTo会替换当前页面，所以先移除当前页面再添加新页面
            if (this.routeHistory.length > 0) {
              this.routeHistory.pop()
            }
            this._pushToHistory(url, params)
            resolve(res)
          },
          fail: err => {
            console.error("redirectTo failed:", err)
            reject(err)
          }
        }
        uni.redirectTo(options)
      })
    },

    // 关闭所有页面，打开到应用内的某个页面
    reLaunch(url: string, params?: Record<string, any>) {
      return new Promise((resolve, reject) => {
        const options: UniApp.ReLaunchOptions = {
          url: params ? `${url}?${this._buildQuery(params)}` : url,
          success: res => {
            // reLaunch会清空所有页面栈，重新开始
            this.routeHistory = []
            this._pushToHistory(url, params)
            resolve(res)
          },
          fail: err => {
            console.error("reLaunch failed:", err)
            reject(err)
          }
        }
        uni.reLaunch(options)
      })
    },

    // 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
    switchTab(url: string) {
      return new Promise((resolve, reject) => {
        const options: UniApp.SwitchTabOptions = {
          url,
          success: res => {
            // switchTab会清空页面栈，只保留tabBar页面
            this.routeHistory = []
            this._pushToHistory(url)
            resolve(res)
          },
          fail: err => {
            console.error("switchTab failed:", err)
            reject(err)
          }
        }
        uni.switchTab(options)
      })
    },

    // 关闭当前页面，返回上一页面或多级页面
    navigateBack(delta: number = 1) {
      return new Promise((resolve, reject) => {
        // 检查是否有足够的页面可以返回
        if (this.routeHistory.length < delta) {
          console.warn(`Cannot navigate back ${delta} pages, only ${this.routeHistory.length} pages in history`)
          // 如果页面栈不够，尝试返回到能返回的最大层数
          delta = Math.max(1, this.routeHistory.length - 1)
        }

        const options: UniApp.NavigateBackOptions = {
          delta,
          success: res => {
            this._popFromHistory(delta)
            resolve(res)
          },
          fail: err => {
            console.error("navigateBack failed:", err)
            reject(err)
          }
        }
        uni.navigateBack(options)
      })
    },

    // 构建查询字符串
    _buildQuery(params: Record<string, any>): string {
      return Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join("&")
    },

    // 清空页面栈（用于登出等场景）
    clearHistory() {
      this.routeHistory = []
      this._saveToStorage()
    },

    // 获取当前页面信息
    getCurrentRoute(): RouteItem | null {
      return this.routeHistory.length > 0 ? this.routeHistory[this.routeHistory.length - 1] : null
    },

    // 获取上一个页面信息
    getPreviousRoute(): RouteItem | null {
      return this.routeHistory.length > 1 ? this.routeHistory[this.routeHistory.length - 2] : null
    },

    // 检查是否可以返回
    canGoBack(delta: number = 1): boolean {
      return this.routeHistory.length > delta
    },

    // 获取页面栈深度
    getStackDepth(): number {
      return this.routeHistory.length
    }
  },

  getters: {
    // 获取完整的页面栈历史
    fullHistory: state => state.routeHistory,

    // 获取最近的几个页面
    recentPages:
      state =>
      (count: number = 5) => {
        return state.routeHistory.slice(-count)
      }
  }
})
