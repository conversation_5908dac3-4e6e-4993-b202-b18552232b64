<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="添加服务类别与项目" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav add-service">
      <view class="service-items">
        <view class="service-item" v-for="(item, index) in serviceItems" :key="index">
          <view class="service-info">
            <view class="service-header">
              <view class="service-name">{{ item.code }}</view>
            </view>
            <view class="service-desc">{{ item.serviceItem || "暂无描述" }}</view>
            <view class="service-unit">单位：{{ item.unit }}</view>
            <view class="service-unit">单价：￥{{ item.unitPrice }}</view>
            <view class="service-status" v-if="isNewItem(item.serviceItemId)">新选择</view>
          </view>

          <view class="service-quantity">
            <view class="quantity-label">数量</view>
            <view class="quantity-control">
              <view class="quantity-btn" @click="decreaseQuantity(index)">-</view>
              <input class="quantity-input" type="number" v-model="item.quantity" @input="updateQuantity(index)" />
              <view class="quantity-btn" @click="increaseQuantity(index)">+</view>
            </view>
          </view>
        </view>
      </view>

      <view class="fixed-bottom">
        <button class="submit-btn" @click="confirmAdd">确认</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { NavBar } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import { showToast } from "@/utils"
import { useWorkOrderDetailStore, type ServiceItemType } from "./work-order-detail-store"

const workOrderDetailStore = useWorkOrderDetailStore()

// 工单ID
const workOrderId = ref("")
const type = ref("add")

// 服务项目列表
const serviceItems = ref<ServiceItemType[]>([])

// 标记是否是新选择的服务项目
const isNewItem = (id: string): boolean => {
  // 检查是否是临时选中的项目
  return workOrderDetailStore.serviceItems.some(item => item.serviceItemId === id && item.isSelected)
}

// 增加数量
const increaseQuantity = (index: number): void => {
  serviceItems.value[index].quantity++
  updateSubtotal(index)
}

// 减少数量
const decreaseQuantity = (index: number): void => {
  if (serviceItems.value[index].quantity > 1) {
    serviceItems.value[index].quantity--
    updateSubtotal(index)
  }
}

// 更新数量
const updateQuantity = (index: number): void => {
  // 确保数量是正整数
  const quantity = parseInt(String(serviceItems.value[index].quantity))
  if (isNaN(quantity) || quantity < 1) {
    serviceItems.value[index].quantity = 1
  }
  updateSubtotal(index)
}

// 更新小计
const updateSubtotal = (index: number): void => {
  const item = serviceItems.value[index]
  item.subtotal = item.unitPrice * item.quantity
}

// 确认添加
const confirmAdd = () => {
  // 检查所有服务项目的数量
  for (const item of serviceItems.value) {
    if (item.quantity <= 0) {
      return showToast("数量必须大于0")
    }
  }

  // 获取当前 store 中的服务项目（非临时选中的）
  // 这里不需要使用结果，因为我们直接处理 serviceItems.value

  // 创建一个新的服务项目数组
  const updatedItems: any[] = []

  // 处理每个服务项目
  for (const item of serviceItems.value) {
    // 创建标准化的服务项目对象
    const standardItem = {
      serviceItemId: item.serviceItemId,
      code: item.code || "",
      serviceClass: item.serviceClass || "",
      serviceItem: item.serviceItem,
      unitPrice: item.unitPrice,
      quantity: item.quantity,
      unit: item.unit,
      subtotal: item.unitPrice * item.quantity, // 重新计算小计
      description: item.description,
      reportUnitSettlementType: item.reportUnitSettlementType,
      reportSettlementId: item.reportSettlementId
    }

    // 添加到更新列表
    updatedItems.push(standardItem)
  }

  // 更新 store 中的服务项目
  // 使用 addServiceItems 会先清除所有临时选中的项目，然后添加新项目
  workOrderDetailStore.addServiceItems(updatedItems)

  showToast("添加成功")

  // 返回处理与报价页面
  uni.navigateBack({
    delta: type.value === "edit" ? 1 : 2 // 编辑返回1级添加返回2级
  })
}

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
  }
  type.value = option?.type || "add"

  // 获取 store 中所有的服务项目
  const allStoreItems = workOrderDetailStore.serviceItems || []

  // 从 store 中获取临时选中的服务项目
  const selectedItems = workOrderDetailStore.getSelectedServiceItems()

  // 获取已有的服务项目（非临时选中的）
  const existingItems = allStoreItems.filter(item => !item.isSelected)

  if (selectedItems && selectedItems.length > 0) {
    // 合并已有项目和新选择的项目
    const mergedItems: ServiceItemType[] = []

    // 先添加已有的项目
    if (existingItems.length > 0) {
      existingItems.forEach(item => {
        mergedItems.push({
          ...item,
          quantity: item.quantity || 1,
          subtotal: item.subtotal || item.unitPrice
        })
      })
    }

    // 再添加新选择的项目
    selectedItems.forEach(item => {
      // 检查是否已经存在于已有项目中
      const existingIndex = mergedItems.findIndex(existing => existing.serviceItemId === item.serviceItemId)

      if (existingIndex === -1) {
        // 如果不存在，添加为新项目
        mergedItems.push({
          ...item,
          quantity: 1,
          subtotal: item.unitPrice
        })
      }
      // 如果已存在，不需要重复添加
    })

    // 更新服务项目列表
    serviceItems.value = mergedItems
  } else if (existingItems.length > 0) {
    // 如果没有新选择的项目，但有已有项目，显示已有项目
    serviceItems.value = existingItems.map(item => ({
      ...item,
      quantity: item.quantity || 1,
      subtotal: item.subtotal || item.unitPrice
    }))
  }
})
</script>

<style lang="scss" scoped>
.add-service {
  background-color: #f5f5f5;
  padding-bottom: 120rpx;

  .service-items {
    margin: 20rpx;
  }

  .service-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .service-info {
      margin-bottom: 30rpx;

      .service-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-name {
          font-size: 30rpx;
          color: #333;
          font-weight: bold;
        }

        .service-id {
          font-size: 26rpx;
          color: #999;
        }
      }

      .service-desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 10rpx;
      }

      .service-unit {
        font-size: 26rpx;
        color: #999;
      }

      .service-status {
        font-size: 24rpx;
        color: #ff9800;
        background-color: #fff9c4;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        display: inline-block;
        margin-top: 8rpx;
      }
    }

    .service-quantity {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .quantity-label {
        font-size: 28rpx;
        color: #333;
      }

      .quantity-control {
        display: flex;
        align-items: center;
        width: 240rpx;
        height: 70rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        overflow: hidden;

        .quantity-btn {
          width: 70rpx;
          height: 70rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 36rpx;
          color: #666;
          background-color: #f5f5f5;
        }

        .quantity-input {
          flex: 1;
          height: 70rpx;
          text-align: center;
          font-size: 28rpx;
          border-left: 1rpx solid #ddd;
          border-right: 1rpx solid #ddd;
        }
      }
    }
  }

  .fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .submit-btn {
      height: 80rpx;
      line-height: 80rpx;
      background-color: $uni-color-primary;
      color: #fff;
      font-size: 30rpx;
      border-radius: 40rpx;
    }
  }
}
</style>
