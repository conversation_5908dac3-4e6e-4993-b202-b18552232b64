<template>
  <view class="nav-bar" :style="{ height: navBarHeight + 'px' }">
    <view
      class="nav-bar-content"
      :style="{ top: statusBarHeight + 'px', height: navBarHeight - statusBarHeight + 'px' }">
      <view class="left-area" @click="goBack" v-if="showBack">
        <image class="back-icon" src="@/static/arrow-left.svg" mode="aspectFit"></image>
      </view>
      <view class="title-area">
        <text class="title">{{ title }}</text>
      </view>
      <view class="right-area" :style="{ width: menuButtonWidth + 'px' }">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script>
import { useSystemStore } from "@/store/system"
export default {
  name: "NavBar",
  props: {
    title: {
      type: String,
      default: ""
    },
    showBack: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44,
      menuButtonWidth: 87 // 默认值
    }
  },
  created() {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    this.navBarHeight = this.statusBarHeight + 44
    useSystemStore().setNavBarHeight(this.navBarHeight)
  },
  methods: {
    goBack() {
      uni.navigateBack({
        fail: () => {
          uni.switchTab({
            url: "/pages/home/<USER>"
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.nav-bar {
  position: relative;
  z-index: 1;
  width: 100%;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
  background: #fff;

  .nav-bar-content {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;

    .left-area {
      position: absolute;
      left: 20rpx;

      .back-icon {
        width: 36rpx;
        height: 36rpx;
      }
    }

    .title-area {
      flex: 1;
      text-align: center;

      .title {
        font-size: 34rpx;
        font-weight: 500;
        color: #333;
      }
    }

    .right-area {
      position: absolute;
      right: 20rpx;
    }
  }
}
</style>
