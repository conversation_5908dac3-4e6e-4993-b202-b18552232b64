import { useUserStore } from "@/store/user"
import { showToast, wxReLaunch } from "../"
import { BASE_URL, BASE_URL_PREFIX, SENSITIVE_WORDS } from "@/configs"

interface RequestHeader extends AnyObject {
  "Content-Type": string
  Authorization?: string
  "X-Requested-Token"?: string
  Cookie?: string // SessionWsid
}

type Method = "OPTIONS" | "GET" | "HEAD" | "POST" | "PUT" | "DELETE" | "TRACE" | "CONNECT" | "PATCH"

type HintType = "toast" | "modal" | "none"

interface RequestOptions {
  header?: AnyObject
  responseType?: string
  hintType?: HintType // 提示类型
  params?: any // 参数
  timeout?: number
  checkSensitive?: boolean
}

export interface Response extends UniApp.RequestSuccessCallbackResult {
  message?: string
  userMessage?: string
  code: number | string
  data: any
  [data: string]: any
}

export class Request {
  baseUrl: string
  urlPrefix: string // url请求前缀 后台和中台不同
  header: RequestHeader

  modalFlag: boolean // modal弹窗的展示
  appId: string
  secretKey: string
  expireTime: number

  constructor() {
    this.baseUrl = BASE_URL
    this.urlPrefix = BASE_URL_PREFIX
    this.header = { "Content-Type": "application/json" }
    this.modalFlag = false

    this.appId = ""
    this.secretKey = ""
    this.expireTime = 0
  }

  get(url: string, options?: RequestOptions) {
    return this.request(url, "GET", options)
  }

  post(url: string, options?: RequestOptions) {
    return this.request(url, "POST", options)
  }

  put(url: string, options?: RequestOptions) {
    return this.request(url, "PUT", options)
  }

  delete(url: string, options?: RequestOptions) {
    return this.request(url, "DELETE", options)
  }

  patch(url: string, options?: RequestOptions) {
    return this.request(url, "PATCH", options)
  }

  setHeader(key: string, value: string) {
    this.header[key] = value
  }

  showErrorMessage(hintType: HintType, message: string) {
    if (hintType === "toast") {
      showToast(message)
    } else if (hintType === "modal") {
      uni.showModal({
        title: "提示",
        content: message,
        showCancel: false
      })
    }
  }

  private request(url: string, method: Method, options?: RequestOptions) {
    return new Promise<Response>((resolve, reject) => {
      // 检查请求体、params、url中是否包含敏感词
      if (options?.checkSensitive && options?.params) {
        const checkSensitive = (value: any) => {
          if (typeof value === "string") return SENSITIVE_WORDS.some(word => value.includes(word))
          if (typeof value === "object") {
            try {
              return SENSITIVE_WORDS.some(word => JSON.stringify(value).includes(word))
            } catch {
              return false
            }
          }
          return false
        }
        if (checkSensitive(options.params)) {
          reject({ message: "请求包含敏感词", code: 500 })
        }
      }
      let requestUrl = this.baseUrl + this.urlPrefix + url
      // #ifdef H5
      requestUrl = this.urlPrefix + url
      // #endif
      let header = options?.header
      let responseType = options?.responseType

      this.setHeader("Authorization", `Bearer ${useUserStore().token}`)

      uni.request({
        header: { ...this.header, ...header },
        // @ts-ignore
        method: method,
        url: requestUrl,
        data: options?.params || {},
        timeout: options?.timeout || 60000,
        responseType: responseType || "text",
        success: response => {
          let result = response as Response
          // 成功
          if (response.statusCode === 200 || response.statusCode === 201) {
            resolve(result)
          }

          // 异常
          else {
            if (result.statusCode === 401) {
              console.log("登录失效")
              useUserStore().clearUserInfo()
              this.setHeader("Authorization", "")
              if (!this.modalFlag) {
                this.modalFlag = true
                showToast(result.data.message || "登录已失效")
              }
              reject({ message: result.data.message || "登录已失效", code: 401 })
            } else {
              const _result = !result.data.message
                ? { message: "系统未知错误", code: 500 }
                : { code: result.statusCode, message: result.data.message }
              this.showErrorMessage(options?.hintType || "none", _result.message)
              reject(_result)
            }
          }
        },
        fail: error => {
          reject(error)
          this.showErrorMessage("toast", error.errMsg)
        }
      })
    })
  }

  // 上传文件
  uploadFile(
    url: string,
    options: {
      filePath?: string
      file?: File
      files?: File[]
      fileName?: string
      formData?: AnyObject
      header?: AnyObject
    }
  ) {
    return new Promise<Response>((resolve, reject) => {
      let uploadUrl = BASE_URL + this.urlPrefix + url
      // #ifdef H5
      uploadUrl = this.urlPrefix + url
      // #endif

      /* ------------ header ------------ */
      let requestHeader: AnyObject = {}
      if (options.header) {
        requestHeader = { ...options.header }
      }

      uni.uploadFile({
        url: uploadUrl,
        filePath: options.filePath,
        files: options.files || [],
        name: options.fileName || "file",
        formData: { ...options.formData },
        file: options?.file,
        header: {
          ...requestHeader,
          Authorization: `Bearer ${useUserStore().token}`
        },
        success: response => {
          const result = JSON.parse(response.data)
          // 成功
          if (response.statusCode === 200 || response.statusCode === 201) {
            resolve(result)
          }
          // 失败
          else {
            reject(result)
          }
        },
        fail: error => {
          reject(error)
        }
      })
    })
  }

  // 下载文件
  downloadFile(url: string, headers?: AnyObject) {
    let requestUrl = BASE_URL + this.urlPrefix + url
  }
}

export const request = new Request()
