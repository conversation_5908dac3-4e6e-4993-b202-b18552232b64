<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="处理与报价" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav process-price">
      <view class="section">
        <view class="form-item">
          <view class="label"><span class="required">*</span>处理意见：</view>
          <textarea v-model="repairPlan.processingOpinion" placeholder="请输入处理意见" maxlength="200" />
        </view>
      </view>

      <!-- 服务类别与项目 -->
      <view class="section">
        <view class="section-header">
          <view class="section-title">服务类别与项目</view>
          <view class="action-container">
            <view v-if="serviceItems.length > 0" class="add-btn" @click="goToAddService">编辑</view>
            <view class="add-btn" @click="goToSelectService">添加</view>
          </view>
        </view>

        <!-- 服务项目列表 -->
        <view class="service-items" v-if="serviceItems.length > 0">
          <view class="service-item" v-for="(item, index) in serviceItems" :key="index">
            <view class="service-item-header">
              <view class="service-id">{{ item.code }}</view>
              <view class="delete-btn" @click="removeServiceItem(item.serviceItemId)">×</view>
            </view>
            <view class="service-item-content">
              <view class="service-name">{{ item.serviceItem }}</view>
            </view>
            <view class="service-item-footer">
              <view class="footer-row">
                <view class="service-price footer-column">单价：¥ {{ item.unitPrice.toFixed(2) }}</view>
                <view class="service-unit footer-column">单位：{{ item.unit }}</view>
              </view>
              <view class="footer-row">
                <view class="service-quantity footer-column">数量：{{ item.quantity }}</view>
                <view class="service-subtotal footer-column">小计：¥ {{ item.subtotal.toFixed(2) }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="empty-tip" v-else>
          <text>暂无服务项目，请点击添加</text>
        </view>
      </view>

      <!-- 配件更换明细 -->
      <!-- <view class="section">
        <view class="section-header">
          <view class="section-title">配件更换明细</view>
          <view class="add-btn">+添加配件</view>
        </view>
        <view class="empty-tip">
          <text>暂无配件，请点击添加配件</text>
        </view>
      </view> -->

      <!-- 交通距离 -->
      <view class="section">
        <view class="form-item">
          <view class="label">交通距离</view>
          <view class="distance-input">
            <input v-model="repairPlan.distance" type="number" placeholder="请输入" />
            <text class="unit">KM</text>
          </view>
        </view>
      </view>

      <!-- 费用统计 -->
      <view class="section">
        <view class="section-title">费用统计</view>
        <view class="fee-item">
          <view class="fee-label">服务费</view>
          <view class="fee-value">¥ {{ repairPlan.serviceFee.toFixed(2) }}</view>
        </view>
        <view class="fee-item">
          <view class="fee-label">交通服务费 (超出50KM，¥ 2.00/KM)</view>
          <view class="fee-value">¥ {{ repairPlan.transportFee.toFixed(2) }}</view>
        </view>
        <view class="fee-item">
          <view class="fee-label">配件价格合计</view>
          <view class="fee-value">¥ {{ repairPlan.partsTotal.toFixed(2) }}</view>
        </view>
        <view class="fee-total">
          <view class="fee-label">合计</view>
          <view class="fee-value total">¥ {{ repairPlan.totalPrice.toFixed(2) }}</view>
        </view>
      </view>

      <view class="bottom-placeholder"></view>

      <view class="fixed-bottom footer-buttons">
        <view class="btn btn-default" @click="goBack">取消</view>
        <view class="btn btn-primary" @click="submitPlan">提交</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue"
import { NavBar } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import { showToast, showLoading, hideLoading } from "@/utils"
import { useWorkOrderDetailStore } from "./work-order-detail-store"
import { useUserStore } from "@/store/user"
import { processAndPrice, getRepairPrice } from "@/api"

const userStore = useUserStore()
const workOrderDetailStore = useWorkOrderDetailStore()

const workOrderId = ref("")

// 维修方案表单
const repairPlan = ref({
  processingOpinion: "",
  totalPrice: 0 as number,
  distance: 0,
  serviceFee: 0,
  transportFee: 0,
  partsTotal: 0
})

// 服务项目列表（只显示非临时选中项）
const serviceItems = computed(() => {
  return workOrderDetailStore.serviceItems.filter(item => !item.isSelected)
})

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
  }

  // 如果是修改，从store中获取已有的维修方案数据
  repairPlan.value.processingOpinion = workOrderDetailStore.processingOpinion
  repairPlan.value.distance = workOrderDetailStore.distance || 0

  // 打印服务项目列表，用于调试
  console.log("服务项目列表:", workOrderDetailStore.serviceItems)
})

// 服务项目和距离边滑需要重新计算价格
watch(
  () => [workOrderDetailStore.serviceItems, repairPlan.value.distance],
  val => {
    if (repairPlan.value.distance < 0) return
    console.log(workOrderDetailStore.serviceItems)
    getRepairPrice({
      serviceItems: workOrderDetailStore.serviceItems,
      settlementType: workOrderDetailStore.serviceItems[0]?.reportUnitSettlementType || "IMMEDIATELY",
      distance: repairPlan.value.distance,
      workOrderId: workOrderId.value
    }).then(res => {
      const data = res.data.data
      repairPlan.value.totalPrice = data.totalPrice || 0
      repairPlan.value.serviceFee = data.serviceFee || 0
      repairPlan.value.transportFee = data.transportFee || 0
      repairPlan.value.partsTotal = data.partsTotal || 0
    })
  },
  { deep: true }
)

// 前往选择服务项目页面
const goToSelectService = () => {
  uni.navigateTo({
    url: `/pages/order-detail/select-service?workOrderId=${workOrderId.value}`
  })
}

// 前往添加服务项目页面
const goToAddService = () => {
  uni.navigateTo({
    url: `/pages/order-detail/add-service?workOrderId=${workOrderId.value}&type=edit`
  })
}

// 移除服务项目
const removeServiceItem = (id: string) => {
  uni.showModal({
    title: "提示",
    content: "确定要删除该服务项目吗？",
    success: res => {
      if (res.confirm) {
        workOrderDetailStore.removeServiceItem(id)
      }
    }
  })
}

// 提交维修方案
const submitPlan = async () => {
  // 表单验证
  if (!repairPlan.value.processingOpinion) {
    return showToast("请输入处理描述")
  }

  if (repairPlan.value.processingOpinion.length > 200) return showToast("处理描述不能超过200个字符")

  if (serviceItems.value.length === 0) {
    return showToast("请添加服务项目")
  }
  if (repairPlan.value.distance < 0) {
    return showToast("交通距离不能为负数")
  }

  try {
    showLoading("提交中...", true)

    const params = {
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      processDescription: repairPlan.value.processingOpinion,
      serviceItems: serviceItems.value,
      distance: repairPlan.value.distance,
      type: "create"
    }
    await processAndPrice(params)

    hideLoading()
    showToast("提交成功")

    // 通知工单详情页刷新
    uni.$emit("refreshOrderDetail")
    uni.$emit("repairOrderListRefresh")

    // 返回工单详情页
    uni.navigateBack()
  } catch (error) {
    hideLoading()
    showToast("提交失败")
    console.error("提交维修方案失败", error)
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.process-price {
  background-color: #f5f5f5;
  padding-bottom: 120rpx;

  .section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 24rpx;
      border-bottom: 1px solid #eee;
      padding-bottom: 16rpx;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      border-bottom: 1px solid #eee;
      padding-bottom: 16rpx;

      .section-title {
        margin-bottom: 0;
        border-bottom: none;
        padding-bottom: 0;
      }

      .action-container {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }

      .add-btn {
        color: $uni-color-primary;
        font-size: 28rpx;
      }
    }
  }

  .form-item {
    margin-bottom: 20rpx;

    .label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
      .required {
        color: #f56c6c;
        margin-right: 10rpx;
      }
    }

    input,
    textarea,
    .picker {
      width: 100%;
      background-color: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }

    input {
      height: 50rpx;
    }
    textarea {
      height: 240rpx;
    }

    .picker {
      height: 80rpx;
      line-height: 80rpx;
      padding: 0 16rpx;
      color: #333;
    }
  }

  .footer-buttons {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;

    .btn {
      margin-left: 20rpx;
      height: 80rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      border-radius: 40rpx;
      flex: 1;
      min-width: 0rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .btn-primary {
      background-color: $uni-color-primary;
      color: #fff;
    }

    .btn-default {
      background-color: #fff;
      color: #666;
      border: 1px solid #ddd;
    }
  }

  .bottom-placeholder {
    height: 100rpx;
  }

  .service-items {
    .service-item {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .service-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-id {
          font-size: 26rpx;
          color: #666;
          font-weight: bold;
        }

        .delete-btn {
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          background-color: #f56c6c;
          color: #fff;
          border-radius: 50%;
          font-size: 32rpx;
        }
      }

      .service-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-name {
          font-size: 28rpx;
          color: #333;
        }
      }

      .service-item-footer {
        display: flex;
        flex-direction: column;
        font-size: 26rpx;
        color: #666;
        .footer-row {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .footer-column {
            min-width: 50%;
            &:last-child {
              text-align: right;
            }
          }
        }
        .service-price {
          color: #f56c6c;
          white-space: nowrap;
        }

        .service-subtotal {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }

  .empty-tip {
    text-align: center;
    padding: 40rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .distance-input {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 8rpx;
    padding-right: 16rpx;

    input {
      flex: 1;
      border: none;
      background-color: transparent;
    }

    .unit {
      font-size: 28rpx;
      color: #666;
    }
  }

  .fee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #eee;

    .fee-label {
      font-size: 28rpx;
      color: #666;
    }

    .fee-value {
      font-size: 28rpx;
      color: #333;
    }
  }

  .fee-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    margin-top: 10rpx;

    .fee-label {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }

    .fee-value {
      font-size: 30rpx;
      color: #f56c6c;
      font-weight: bold;

      &.total {
        font-size: 36rpx;
      }
    }
  }
}
</style>
