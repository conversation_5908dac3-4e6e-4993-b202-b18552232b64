import { SettlementTypeEnum } from "@/configs"
import { defineStore } from "pinia"

export interface WorkOrderDetail {
  workOrderId: string
  maintenanceUnitId?: string
  maintenanceUserId?: string
  reportUnitId: string
  detailLocation: string
  reporterId: string
  reporterPhone: string
  serviceClass: string
  reportWay: "PHONE" | "SELF_REPORT"
  faultDesc: string
  deviceInfo?: string
  attachments: Array<{
    fileName: string
    fileId: string
    fileType: string
  }>
  reportTime: number
  status: string
  repairResult?: string
  repairTime?: number
  completeTime?: number
  repairScore?: number
  evaluation?: string
  serviceItems?: Array<ServiceItemType>
  reportUnitSettlementType: SettlementTypeEnum
  parts?: Array<{
    id: string
    type: string
    name: string
    specification?: string
    model?: string
    unit?: string
    costPrice: number
    sellingPrice: number
    paymentMethod: "PLATFORM" | "MAINTENANCE_UNIT"
  }>
  processingOpinion?: string
  repairProcess?: Array<{
    processTime: number
    processor: string
    processorName?: string
    processType: string
    processResult: string
    location?: string
  }>
  reporter?: {
    username: string
  }
  reportEnterprise?: {
    companyName: string
  }
  totalPrice?: number | ""
  distance?: number | ""
  serviceFee?: number | ""
  transportFee?: number | ""
  partsTotal?: number | ""
}

export interface ServiceItemType {
  serviceItemId: string
  code: string
  serviceClass: string
  serviceItem: string
  unitPrice: number
  quantity: number
  unit: string
  subtotal: number
  description?: string
  isSelected?: boolean
  reportSettlementId?: string
  reportUnitSettlementType?: SettlementTypeEnum
  maintenanceUnitSettlementType?: SettlementTypeEnum
  [key: string]: any
}

export const useWorkOrderDetailStore = defineStore("WORK_ORDER_DETAIL", {
  state: () => ({
    workOrderId: uni.getStorageSync("workOrderId") || "",
    maintenanceUnitId: uni.getStorageSync("maintenanceUnitId") || "",
    maintenanceUserId: uni.getStorageSync("maintenanceUserId") || "",
    reportUnitId: uni.getStorageSync("reportUnitId") || "",
    detailLocation: uni.getStorageSync("detailLocation") || "",
    reporterId: uni.getStorageSync("reporterId") || "",
    reporterPhone: uni.getStorageSync("reporterPhone") || "",
    serviceClass: uni.getStorageSync("serviceClass") || "",
    reportWay: uni.getStorageSync("reportWay") || "SELF_REPORT",
    faultDesc: uni.getStorageSync("faultDesc") || "",
    deviceInfo: uni.getStorageSync("deviceInfo") || "",
    attachments: uni.getStorageSync("attachments") ? JSON.parse(uni.getStorageSync("attachments")) : [],
    reportTime: uni.getStorageSync("reportTime") || 0,
    status: uni.getStorageSync("status") || "",
    repairResult: uni.getStorageSync("repairResult") || "",
    repairTime: uni.getStorageSync("repairTime") || 0,
    completeTime: uni.getStorageSync("completeTime") || 0,
    repairScore: uni.getStorageSync("repairScore") || 0,
    evaluation: uni.getStorageSync("evaluation") || "",
    serviceItems: uni.getStorageSync("serviceItems") ? JSON.parse(uni.getStorageSync("serviceItems")) : [],
    parts: uni.getStorageSync("parts") ? JSON.parse(uni.getStorageSync("parts")) : [],
    processingOpinion: uni.getStorageSync("processingOpinion") || "",
    totalPrice: uni.getStorageSync("totalPrice") || "",
    distance: uni.getStorageSync("distance") || 0,
    serviceFee: uni.getStorageSync("serviceFee") || 0,
    transportFee: uni.getStorageSync("transportFee") || 0,
    partsTotal: uni.getStorageSync("partsTotal") || 0,
    reportUnitSettlementType: uni.getStorageSync("reportUnitSettlementType") || SettlementTypeEnum.IMMEDIATELY,
    repairProcess: uni.getStorageSync("repairProcess") ? JSON.parse(uni.getStorageSync("repairProcess")) : [],
    reporter: uni.getStorageSync("reporter") ? JSON.parse(uni.getStorageSync("reporter")) : { username: "" },
    reportEnterprise: uni.getStorageSync("reportEnterprise")
      ? JSON.parse(uni.getStorageSync("reportEnterprise"))
      : { companyName: "" }
  }),
  actions: {
    setWorkOrderDetail(detail: WorkOrderDetail) {
      this.workOrderId = detail.workOrderId || ""
      this.maintenanceUnitId = detail.maintenanceUnitId || ""
      this.maintenanceUserId = detail.maintenanceUserId || ""
      this.reportUnitId = detail.reportUnitId || ""
      this.detailLocation = detail.detailLocation || ""
      this.reporterId = detail.reporterId || ""
      this.reporterPhone = detail.reporterPhone || ""
      this.serviceClass = detail.serviceClass || ""
      this.reportWay = detail.reportWay || "SELF_REPORT"
      this.faultDesc = detail.faultDesc || ""
      this.deviceInfo = detail.deviceInfo || ""
      this.attachments = detail.attachments || []
      this.reportTime = detail.reportTime || 0
      this.status = detail.status || ""
      this.repairResult = detail.repairResult || ""
      this.repairTime = detail.repairTime || 0
      this.completeTime = detail.completeTime || 0
      this.repairScore = detail.repairScore || 0
      this.evaluation = detail.evaluation || ""
      this.serviceItems = detail.serviceItems || []
      this.parts = detail.parts || []
      this.processingOpinion = detail.processingOpinion || ""
      this.repairProcess = detail.repairProcess || []
      this.reporter = detail.reporter || { username: "" }
      this.reportEnterprise = detail.reportEnterprise || { companyName: "" }
      this.totalPrice = detail.totalPrice || ""
      this.distance = detail.distance || 0
      this.serviceFee = detail.serviceFee || 0
      this.transportFee = detail.transportFee || 0
      this.partsTotal = detail.partsTotal || 0
      this.reportUnitSettlementType = detail.reportUnitSettlementType || SettlementTypeEnum.IMMEDIATELY

      uni.setStorageSync("workOrderId", this.workOrderId)
      uni.setStorageSync("maintenanceUnitId", this.maintenanceUnitId)
      uni.setStorageSync("maintenanceUserId", this.maintenanceUserId)
      uni.setStorageSync("reportUnitId", this.reportUnitId)
      uni.setStorageSync("detailLocation", this.detailLocation)
      uni.setStorageSync("reporterId", this.reporterId)
      uni.setStorageSync("reporterPhone", this.reporterPhone)
      uni.setStorageSync("serviceClass", this.serviceClass)
      uni.setStorageSync("reportWay", this.reportWay)
      uni.setStorageSync("faultDesc", this.faultDesc)
      uni.setStorageSync("deviceInfo", this.deviceInfo)
      uni.setStorageSync("attachments", JSON.stringify(this.attachments))
      uni.setStorageSync("reportTime", this.reportTime)
      uni.setStorageSync("status", this.status)
      uni.setStorageSync("repairResult", this.repairResult)
      uni.setStorageSync("repairTime", this.repairTime)
      uni.setStorageSync("completeTime", this.completeTime)
      uni.setStorageSync("repairScore", this.repairScore)
      uni.setStorageSync("evaluation", this.evaluation)
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
      uni.setStorageSync("parts", JSON.stringify(this.parts))
      uni.setStorageSync("processingOpinion", this.processingOpinion)
      uni.setStorageSync("repairProcess", JSON.stringify(this.repairProcess))
      uni.setStorageSync("reporter", JSON.stringify(this.reporter))
      uni.setStorageSync("reportEnterprise", JSON.stringify(this.reportEnterprise))
      uni.setStorageSync("totalPrice", this.totalPrice)
      uni.setStorageSync("distance", this.distance)
      uni.setStorageSync("serviceFee", this.serviceFee)
      uni.setStorageSync("transportFee", this.transportFee)
      uni.setStorageSync("partsTotal", this.partsTotal)
      uni.setStorageSync("reportUnitSettlementType", this.reportUnitSettlementType)
    },
    // 添加服务项目
    addServiceItem(item: ServiceItemType) {
      // 检查是否已存在相同ID的非临时项目
      const existingIndex = this.serviceItems.findIndex(i => i.serviceItemId === item.serviceItemId && !i.isSelected)

      if (existingIndex >= 0) {
        // 如果已存在，更新数量和小计
        this.serviceItems[existingIndex].quantity += item.quantity
        this.serviceItems[existingIndex].subtotal =
          this.serviceItems[existingIndex].unitPrice * this.serviceItems[existingIndex].quantity
      } else {
        // 如果不存在，添加新项目（确保不是临时选中项）
        this.serviceItems.push({
          ...item,
          isSelected: false // 确保不是临时选中项
        })
      }
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 批量添加服务项目
    addServiceItems(items: Array<ServiceItemType>) {
      // 清除临时选中的服务项目
      this.serviceItems = this.serviceItems.filter(item => !item.isSelected)

      // 添加新的服务项目（确保不是临时选中项）
      items.forEach(item => {
        const newItem = {
          ...item,
          isSelected: false // 确保不是临时选中项
        }
        const existingIndex = this.serviceItems.findIndex(i => i.serviceItemId === newItem.serviceItemId)
        if (existingIndex !== -1) {
          this.serviceItems[existingIndex] = newItem
        } else {
          this.serviceItems.push(newItem)
        }
      })
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 移除服务项目
    removeServiceItem(id: string) {
      this.serviceItems = this.serviceItems.filter(item => item.serviceItemId !== id)
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 设置临时选中的服务项目
    setSelectedServiceItems(items: Array<ServiceItemType>) {
      // 清除之前的临时选中项和取消选中的项
      const oldServiceItems = this.serviceItems.filter(item => {
        return !item.isSelected && items.find(i => i.serviceItemId === item.serviceItemId)
      })

      // 添加新的临时选中项
      let selectedItems = items.map(item => ({
        ...item,
        quantity: 0, // 临时项数量设为0
        subtotal: 0, // 临时项小计设为0
        isSelected: true // 标记为临时选中项
      }))

      // 新添加的临时选项中过滤已经添加过的选项
      selectedItems = selectedItems.filter(item => {
        const existingIndex = oldServiceItems.findIndex(i => i.serviceItemId === item.serviceItemId && !i.isSelected)
        return existingIndex === -1
      })

      this.serviceItems = [...oldServiceItems, ...selectedItems]
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
    },

    // 获取临时选中的服务项目
    getSelectedServiceItems() {
      return this.serviceItems.filter(item => item.isSelected)
    },

    // 清除临时选中的服务项目
    clearSelectedServiceItems() {
      this.serviceItems = this.serviceItems.filter(item => !item.isSelected)
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
    },

    // 计算总价
    calculateTotalPrice() {
      // 计算服务项目总价（只计算非临时选中项）
      const serviceItemsTotal = this.serviceItems.reduce((total, item) => {
        // 只计算非临时选中项的小计
        return total + (item.isSelected ? 0 : item.subtotal)
      }, 0)

      // 计算配件总价
      const partsTotal = this.parts.reduce((total, item) => {
        return total + (item.sellingPrice || 0)
      }, 0)

      // 设置总价
      this.totalPrice = serviceItemsTotal + partsTotal
      uni.setStorageSync("totalPrice", this.totalPrice)
    },
    // 清空工单详情
    clearWorkOrderDetail() {
      this.workOrderId = ""
      this.maintenanceUnitId = ""
      this.maintenanceUserId = ""
      this.reportUnitId = ""
      this.detailLocation = ""
      this.reporterId = ""
      this.reporterPhone = ""
      this.serviceClass = ""
      this.reportWay = "SELF_REPORT"
      this.faultDesc = ""
      this.deviceInfo = ""
      this.attachments = []
      this.reportTime = 0
      this.status = ""
      this.repairResult = ""
      this.repairTime = 0
      this.completeTime = 0
      this.repairScore = 0
      this.evaluation = ""
      this.serviceItems = []
      this.parts = []
      this.processingOpinion = ""
      this.repairProcess = []
      this.reporter = { username: "" }
      this.reportEnterprise = { companyName: "" }
      this.totalPrice = ""
      this.distance = 0
      this.serviceFee = 0
      this.transportFee = 0
      this.partsTotal = 0
      this.reportUnitSettlementType = SettlementTypeEnum.IMMEDIATELY

      uni.removeStorageSync("workOrderId")
      uni.removeStorageSync("maintenanceUnitId")
      uni.removeStorageSync("maintenanceUserId")
      uni.removeStorageSync("reportUnitId")
      uni.removeStorageSync("detailLocation")
      uni.removeStorageSync("reporterId")
      uni.removeStorageSync("reporterPhone")
      uni.removeStorageSync("serviceClass")
      uni.removeStorageSync("reportWay")
      uni.removeStorageSync("faultDesc")
      uni.removeStorageSync("deviceInfo")
      uni.removeStorageSync("attachments")
      uni.removeStorageSync("reportTime")
      uni.removeStorageSync("status")
      uni.removeStorageSync("repairResult")
      uni.removeStorageSync("repairTime")
      uni.removeStorageSync("completeTime")
      uni.removeStorageSync("repairScore")
      uni.removeStorageSync("evaluation")
      uni.removeStorageSync("serviceItems")
      uni.removeStorageSync("parts")
      uni.removeStorageSync("processingOpinion")
      uni.removeStorageSync("repairProcess")
      uni.removeStorageSync("reporter")
      uni.removeStorageSync("reportEnterprise")
      uni.removeStorageSync("totalPrice")
      uni.removeStorageSync("distance")
      uni.removeStorageSync("serviceFee")
      uni.removeStorageSync("transportFee")
      uni.removeStorageSync("partsTotal")
      uni.removeStorageSync("reportUnitSettlementType")
    }
  }
})
