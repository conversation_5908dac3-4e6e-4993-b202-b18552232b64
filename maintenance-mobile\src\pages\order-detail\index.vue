<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="工单详情" />

    <!-- 页面内容 -->
    <scroll-view
      class="page-content-with-nav repair-detail"
      scroll-y="true"
      refresher-enabled="true"
      :refresher-triggered="refresherTriggered"
      @refresherpulling="onPulling"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      @refresherabort="onAbort">
      <form @submit="submitForm">
        <!-- 基本信息 -->
        <view class="section">
          <view class="section-title">基本信息</view>
          <view class="info-item">
            <view class="label">工单号</view>
            <view class="value">{{ workOrderDetail?.workOrderId }}</view>
          </view>
          <view class="info-item">
            <view class="label">工单状态</view>
            <view class="tag" :class="`tag-${showStatus.type}`">{{ showStatus.label }}</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"><text v-if="isEditable" class="required">*</text> 详细地址</view>
            <input
              v-if="isEditable"
              class="input"
              v-model="formData.detailLocation"
              placeholder="请输入详细地址"
              maxlength="100" />
            <view v-else class="value">{{ workOrderDetail?.detailLocation || "--" }}</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"><text v-if="isEditable" class="required">*</text> 报修类型</view>
            <picker
              v-if="isEditable"
              @change="onServiceClassChange"
              :value="serviceClassIndex"
              :range="serviceClassOptions"
              range-key="serviceClass"
              :disabled="serviceClassLoading || serviceClassOptions.length === 0">
              <view
                class="picker-value"
                :class="{
                  placeholder: !formData.serviceClass,
                  loading: serviceClassLoading,
                  empty: serviceClassOptions.length === 0
                }"
                @click="
                  e => {
                    if (serviceClassOptions.length === 0) showToast('暂无报修类型数据')
                  }
                ">
                <view>
                  <uni-icons
                    v-if="serviceClassLoading"
                    type="spinner-cycle"
                    size="16"
                    color="#999"
                    class="loading-icon"></uni-icons>
                  <text v-if="serviceClassLoading">加载中...</text>
                  <text v-else>{{ getServiceClassName() || "请选择" }}</text>
                </view>
                <uni-icons
                  type="right"
                  size="16"
                  color="#999"
                  v-if="!serviceClassLoading && serviceClassOptions.length > 0"></uni-icons>
              </view>
            </picker>
            <view v-else class="value">{{ workOrderDetail?.serviceClass || "--" }}</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"><text v-if="isEditable" class="required">*</text> 故障描述</view>
            <textarea
              v-if="isEditable"
              class="textarea"
              v-model="formData.faultDesc"
              placeholder="请描述故障情况"
              maxlength="200" />
            <view v-else class="value">{{ workOrderDetail?.faultDesc || "--" }}</view>
            <view v-if="isEditable" class="word-count">{{ formData.faultDesc.length }}/200</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label">机器信息</view>
            <textarea
              v-if="isEditable"
              class="textarea"
              v-model="formData.deviceInfo"
              placeholder="请输入机器信息（选填）"
              maxlength="200" />
            <view v-else class="value">{{ workOrderDetail?.deviceInfo || "--" }}</view>
            <view v-if="isEditable" class="word-count">{{ formData.deviceInfo.length }}/200</view>
          </view>

          <view class="info-item">
            <view class="label">报修时间</view>
            <view class="value">{{ formatTime(workOrderDetail?.reportTime) || "--" }}</view>
          </view>
        </view>

        <!-- 报修人信息 -->
        <view class="section">
          <view class="section-title">报修人信息</view>
          <view class="info-item">
            <view class="label">报修人</view>
            <view class="value">{{ workOrderDetail?.reporter?.username || "--" }}</view>
          </view>
          <view class="info-item">
            <view class="label">联系电话</view>
            <view class="value">
              {{ workOrderDetail?.reporterPhone || "--" }}
            </view>
          </view>
          <view class="info-item">
            <view class="label">报修单位</view>
            <view class="value">{{ workOrderDetail?.reportEnterprise?.companyName || "--" }}</view>
          </view>
        </view>

        <!-- 附件信息 -->
        <view class="section" v-if="showAttachment.length > 0 || isEditable">
          <view class="section-title">附件信息</view>
          <view v-if="isEditable" class="section-hint">请注意上传的数据中是否包含敏感信息</view>

          <view class="upload-list">
            <view class="image-item" v-for="(item, index) in showAttachment" :key="index">
              <template v-if="isImage(item.fileType)">
                <BlobImage
                  v-if="item.status === 'success'"
                  style="width: 200rpx; height: 200rpx"
                  :file-id="item.fileId" />
                <image v-else :src="item.tempPath" style="width: 200rpx; height: 200rpx" mode="aspectFit"></image>
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>
              <template v-else-if="isVideo(item.fileType)">
                <BlobVideo style="width: 200rpx; height: 200rpx" :file-id="item.fileId" />
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>
              <view class="file-name">{{ item.fileName }}</view>
              <text v-if="isEditable" class="delete-icon" @click.stop="deleteImage(item)">×</text>
            </view>
            <view class="upload-item" v-if="isEditable" @click="chooseAttachment">
              <text class="upload-icon">+</text>
            </view>
          </view>
        </view>

        <!-- 维修方案与价格 -->
        <view class="section" v-if="workOrderDetail?.processingOpinion || workOrderDetail?.totalPrice">
          <view class="section-title">维修方案与价格</view>

          <!-- 处理意见 -->
          <view class="info-item">
            <view class="label">结算方式：</view>
            <view class="value">{{
              getSettlementType(workOrderDetail?.serviceItems?.[0]?.reportUnitSettlementType) || "--"
            }}</view>
          </view>

          <!-- 处理意见 -->
          <view class="info-item">
            <view class="label">处理意见：</view>
            <view class="value">{{ workOrderDetail.processingOpinion || "暂无处理意见" }}</view>
          </view>

          <!-- 服务类别与项目 -->
          <view class="subsection" v-if="workOrderDetail.serviceItems && workOrderDetail.serviceItems.length > 0">
            <view class="subsection-title">服务类别与项目：</view>
            <view class="service-items">
              <view class="service-item" v-for="(item, index) in workOrderDetail.serviceItems" :key="index">
                <view class="service-item-header">
                  <view class="service-id">{{ item.code }}</view>
                </view>
                <view class="service-item-content">
                  <view class="service-name">{{ item.serviceItem }}</view>
                </view>
                <view class="service-item-footer">
                  <view class="footer-row">
                    <view class="service-price footer-column">单价：¥ {{ formatPrice(item.unitPrice) }}</view>
                    <view class="service-unit footer-column">单位：{{ item.unit }}</view>
                  </view>
                  <view class="footer-row">
                    <view class="service-quantity footer-column">数量：{{ item.quantity }}</view>
                    <view class="service-subtotal footer-column">小计：¥ {{ formatPrice(item.subtotal) }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 配件更换明细 -->
          <view class="subsection" v-if="workOrderDetail.parts && workOrderDetail.parts.length > 0">
            <view class="subsection-title">配件更换明细：</view>
            <view class="service-items">
              <view class="service-item" v-for="(item, index) in workOrderDetail.parts" :key="index">
                <view class="service-item-header">
                  <view class="service-id">{{ item.id }}</view>
                </view>
                <view class="service-item-content">
                  <view class="service-name">{{ item.name }}</view>
                </view>
                <view class="service-item-footer">
                  <view class="footer-row" v-if="item.specification">
                    <view class="footer-column">规格：{{ item.specification }}</view>
                  </view>
                  <view class="footer-row">
                    <view class="service-price footer-column">单价：¥ {{ formatPrice(item.sellingPrice) }}</view>
                    <view class="service-unit footer-column" v-if="item.unit">单位：{{ item.unit }}</view>
                  </view>
                  <view class="footer-row" v-if="item.quantity">
                    <view class="service-quantity footer-column">数量：{{ item.quantity }}</view>
                    <view class="service-subtotal footer-column"
                      >小计：¥ {{ formatPrice(item.sellingPrice * (item.quantity || 1)) }}</view
                    >
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 交通距离 -->
          <view class="info-item" v-if="workOrderDetail.distance || workOrderDetail.distance === 0">
            <view class="label">交通距离：</view>
            <view class="value">{{ workOrderDetail.distance }} KM</view>
          </view>

          <!-- 费用统计 -->
          <view class="fee-statistics">
            <view class="section-title">费用统计</view>
            <view class="fee-item" v-if="workOrderDetail.serviceFee !== undefined">
              <view class="fee-label">服务费</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.serviceFee) }}</view>
            </view>
            <view class="fee-item" v-if="workOrderDetail.transportFee !== undefined">
              <view class="fee-label">交通服务费 (超出50KM，¥ 2.00/KM)</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.transportFee) }}</view>
            </view>
            <view class="fee-item" v-if="workOrderDetail.partsTotal !== undefined">
              <view class="fee-label">配件价格合计</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.partsTotal) }}</view>
            </view>
            <view class="fee-total">
              <view class="fee-label">合计</view>
              <view class="fee-value total">¥ {{ formatPrice(workOrderDetail.totalPrice) }}</view>
            </view>
          </view>
        </view>

        <!-- 处理信息 -->
        <view class="section" v-if="workOrderDetail?.repairProcess && workOrderDetail.repairProcess.length > 0">
          <view class="section-title">处理信息</view>
          <view class="timeline">
            <view class="timeline-item" v-for="(item, index) in workOrderDetail?.repairProcess" :key="index">
              <view class="time">{{ `${item.processorName} ${formatTime(item.processTime)}` }}</view>
              <view class="content">{{ `${item.processType} ${item.processResult}` }}</view>

              <view v-if="showContentMark(item)" class="content-mark">
                <view v-if="item.location" class="location">{{ `地点：${item.location}` }}</view>
                <view v-if="item.reason" class="reason">{{ `原因：${item.reason}` }}</view>
                <view v-if="item.remark" class="remark">{{ `备注：${item.remark}` }}</view>
                <view v-if="item.repairResult" class="result">{{ `维修结果：${item.repairResult}` }}</view>
                <view v-if="item.evaluation" class="evaluation">{{ `评价：${item.evaluation}` }}</view>
                <view v-if="item.repairScore" class="repair-score">{{ `评分：${item.repairScore}` }}</view>
                <view v-if="item.signatureBase64" class="signature-base64">
                  <view class="signature-base64-title">签名：</view>
                  <image :src="item.signatureBase64" mode="aspectFit"></image>
                </view>
                <!-- 附件信息 -->
                <view v-if="item.attachments && item.attachments.length > 0" class="attachment-container">
                  <view class="attachment-title">附件：</view>
                  <view class="attachment-list">
                    <view class="attachment-item" v-for="(attachment, index) in item.attachments" :key="index">
                      <template v-if="isImage(attachment.fileType)">
                        <BlobImage style="width: 120rpx; height: 120rpx" :file-id="attachment.fileId" />
                      </template>
                      <BlobVideo
                        v-else-if="isVideo(attachment.fileType)"
                        style="width: 120rpx; height: 120rpx"
                        :file-id="attachment.fileId" />
                      <view class="file-name">{{ attachment.fileName }}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部占位 -->
        <view class="bottom-placeholder"></view>

        <!-- 底部按钮 -->
        <view v-if="showFooterButtons" class="fixed-bottom footer-buttons">
          <button v-if="isEditable" class="btn btn-primary" form-type="submit">保存修改</button>
          <view v-if="canContactReporter" class="btn btn-default" @click="contactReporter">联系报修人</view>

          <template v-if="workOrderDetail?.maintenanceUserId === userStore.userId">
            <!-- 待维修人员上门状态的按钮 -->
            <block v-if="workOrderDetail?.status === 'WAITING_REPAIR_PERSON_COME'">
              <view class="btn btn-default" @click="handleNoNeedRepair">无需维修</view>
              <view class="btn btn-primary" @click="handleConfirmArrival">确认到达</view>
            </block>

            <!-- 待出具维修方案状态的按钮 -->
            <block v-if="workOrderDetail?.status === 'WAITING_REPAIR_PLAN'">
              <view class="btn btn-primary" @click="createRepairPlan">处理与报价</view>
            </block>

            <!-- 待修改维修方案状态的按钮 -->
            <block v-if="workOrderDetail?.status === 'WAITING_REPAIR_PLAN_MODIFY'">
              <view class="btn btn-primary" @click="modifyRepairPlan">修改维修报价</view>
            </block>

            <!-- 维修中状态的按钮 -->
            <block v-if="workOrderDetail?.status === 'PROCESSING'">
              <view class="btn btn-default" @click="navigateToReturnOrder">退单</view>
              <view class="btn btn-primary" @click="finishRepair">完成维修</view>
            </block>
          </template>

          <!-- 待企业派单状态的按钮 -->
          <block v-if="workOrderDetail?.status === 'WAITING_COMPANY_DISPATCH'">
            <view class="btn btn-primary" @click="dispatchOrder">派单</view>
          </block>
        </view>
      </form>
    </scroll-view>

    <!-- 无需维修弹窗 -->
    <uni-popup ref="noNeedRepairPopup" type="dialog">
      <view class="custom-popup">
        <view class="popup-title">无需维修原因</view>
        <view class="popup-content">
          <textarea v-model="noNeedRepairReason" placeholder="请输入无需维修的原因" />
        </view>
        <view class="popup-buttons">
          <button class="popup-btn popup-btn-cancel" @click="closeNoNeedRepairPopup">取消</button>
          <button class="popup-btn popup-btn-confirm" @click="submitNoNeedRepair">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 退单弹窗 -->
    <uni-popup ref="cancelOrderPopup" type="dialog">
      <view class="custom-popup">
        <view class="popup-title">退单原因</view>
        <view class="popup-content">
          <textarea v-model="cancelOrderReason" placeholder="请输入退单原因" />
        </view>
        <view class="popup-buttons">
          <button class="popup-btn popup-btn-cancel" @click="closeCancelOrderPopup">取消</button>
          <button class="popup-btn popup-btn-confirm" @click="submitCancelOrder">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue"
import { NavBar, BlobImage, BlobVideo } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import {
  getWorkOrderDetailApi,
  setMaintenanceFree,
  confirmArrival,
  returnOrder,
  getServiceContentApi,
  editWorkOrderApi,
  uploadWorkOrderAttachmentApi,
  deleteFile
} from "@/api"
import { getWorkOrderStatus, formatTime, showToast, showLoading, hideLoading, getSettlementType } from "@/utils"
import { useUserStore } from "@/store/user"
import { useWorkOrderDetailStore } from "./work-order-detail-store"
import { WorkOrderStatusEnum } from "@/configs"

const userStore = useUserStore()
const workOrderDetailStore = useWorkOrderDetailStore()

// 工单详情数据
const workOrderId = ref("")
const workOrderDetail = ref<any>(null)
const showAttachment = ref<Array<any>>([])
const refresherTriggered = ref(false)

// 编辑相关
const isEditable = ref(false)
const formData = ref({
  detailLocation: "",
  serviceClass: "",
  faultDesc: "",
  deviceInfo: "",
  attachments: [] as any[]
})

// 报修类型选择
const serviceClassOptions = ref<any[]>([])
const serviceClassIndex = ref(0)
const serviceClassLoading = ref(false)

// 弹窗相关
const noNeedRepairPopup = ref<any>(null)
const noNeedRepairReason = ref("")
const cancelOrderPopup = ref<any>(null)
const cancelOrderReason = ref("")

// 计算属性
const showStatus = computed(() => {
  return getWorkOrderStatus(workOrderDetail.value?.status)
})

// 是否可以联系报修人
const canContactReporter = computed(() => {
  return true // 所有状态都可以联系报修人
})

// 页面加载
onLoad(async option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
    await getWorkOrderDetail()
  }
})

// 确保弹窗组件正确初始化
onMounted(() => {
  // 等待DOM更新后再获取refs
  setTimeout(() => {
    if (!noNeedRepairPopup.value || !cancelOrderPopup.value) {
      console.warn("某些弹窗组件未正确初始化")
    }
  }, 500)

  // 监听刷新事件，用于处理与报价页面返回后刷新工单详情
  uni.$on("refreshOrderDetail", () => {
    getWorkOrderDetail()
  })
})

// 组件卸载时清除事件监听
onUnmounted(() => {
  uni.$off("refreshOrderDetail")
})

// 获取工单详情
const getWorkOrderDetail = async () => {
  showLoading("获取详情中...", true)
  try {
    // 清空store中的工单详情，避免上一次的数据影响
    workOrderDetailStore.clearWorkOrderDetail()

    const res = await getWorkOrderDetailApi(workOrderId.value)
    workOrderDetail.value = res.data.data

    // 初始化表单数据
    formData.value.detailLocation = workOrderDetail.value.detailLocation || ""
    formData.value.serviceClass = workOrderDetail.value.serviceClass || ""
    formData.value.faultDesc = workOrderDetail.value.faultDesc || ""
    formData.value.deviceInfo = workOrderDetail.value.deviceInfo || ""

    // 初始化附件
    if (workOrderDetail.value.attachments) {
      showAttachment.value = workOrderDetail.value.attachments.map((item: any) => ({
        ...item,
        status: "success"
      }))
      formData.value.attachments = [...workOrderDetail.value.attachments]
    }

    // 根据状态判断是否可编辑
    isEditable.value =
      workOrderDetail.value.status === WorkOrderStatusEnum.WAITING_DISPATCH &&
      workOrderDetail.value.creatorId === userStore.userId

    // 如果可编辑，获取报修类型选项
    if (isEditable.value) {
      await getServiceClassOptions()
    }

    // 将获取到的工单详情存入store
    workOrderDetailStore.setWorkOrderDetail(workOrderDetail.value)
  } catch (error) {
    console.error("获取工单详情失败", error)
    showToast("获取工单详情失败")
  } finally {
    hideLoading()
  }
}

// 获取报修类型选项
const getServiceClassOptions = async () => {
  try {
    serviceClassLoading.value = true
    const res = await getServiceContentApi()
    serviceClassOptions.value = res.data.data || []
    // 设置当前选中的索引
    serviceClassIndex.value = serviceClassOptions.value.findIndex(
      (item: any) => item.id === formData.value.serviceClass
    )
  } catch (error) {
    console.error("获取报修类型失败", error)
  } finally {
    serviceClassLoading.value = false
  }
}

// 获取报修类型名称
const getServiceClassName = () => {
  if (!formData.value.serviceClass || serviceClassOptions.value.length === 0) return ""
  const selected = serviceClassOptions.value.find((item: any) => item.id === formData.value.serviceClass)
  return selected ? selected.serviceClass : ""
}

// 报修类型变更
const onServiceClassChange = (e: any) => {
  const index = e.detail.value
  serviceClassIndex.value = index
  const selected = serviceClassOptions.value[index]
  formData.value.serviceClass = selected.id
}

const showFooterButtons = computed(() => {
  return !(
    workOrderDetail.value?.status === WorkOrderStatusEnum.FINISHED ||
    workOrderDetail.value?.status === WorkOrderStatusEnum.CANCELLED ||
    workOrderDetail.value?.status === WorkOrderStatusEnum.NO_NEED_REPAIR
  )
})

// 判断是否显示内容标记
const showContentMark = (item: any) => {
  return (
    item.location || item.remark || item.repairResult || item.evaluation || item.repairScore || item.signatureBase64
  )
}

// 判断文件类型
const isImage = (fileType: string) => {
  return fileType && fileType.includes("image")
}

const isVideo = (fileType: string) => {
  return fileType && fileType.includes("video")
}

/* ======================================= 附件处理 ======================================= */
// 选择附件类型
const chooseAttachment = () => {
  if (showAttachment.value.length >= 9) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.showActionSheet({
    itemList: ["上传图片", "上传视频"],
    title: "",
    success: res => {
      if (res.tapIndex === 0) {
        chooseImage()
      } else if (res.tapIndex === 1) {
        chooseVideo()
      }
    }
  })
}

// 选择图片
const chooseImage = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseImage({
    count: remainCount,
    success: res => {
      const tempFiles = Array.isArray(res.tempFiles) ? res.tempFiles : [res.tempFiles]
      tempFiles.forEach(file => {
        // #ifdef APP
        file.type = "image/png"
        file.name = `附件${new Date().getTime()}.png}`
        // #endif
        if (file.size > 10 * 1024 * 1024) {
          showToast("文件大小不能超过10M")
          return
        }
        let data = {
          tempPath: file.path,
          fileType: file.type,
          fileName: file.name,
          fileId: "",
          status: "uploading",
          message: "上传中..."
        }

        showAttachment.value.push(data)
        uploadAttachment(file)
      })
    },
    fail: () => {
      showToast("选择图片失败")
    }
  })
}

// 选择视频
const chooseVideo = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseVideo({
    count: 1,
    success: res => {
      let file = res.tempFile
      file.path = res.tempFilePath
      // #ifdef APP
      file = {
        path: res.tempFilePath,
        type: "video/mp4",
        name: `附件${new Date().getTime()}.mp4`,
        size: res.size
      }
      file.path = res.tempFilePath
      // #endif
      if (file.size > 10 * 1024 * 1024) {
        showToast("文件大小不能超过10M")
        return
      }
      if (file.name.indexOf(".mov") || file.name.indexOf(".MOV")) {
        file.type = "video/quicktime"
      }
      let data = {
        tempPath: file.path,
        fileType: file.type,
        fileName: file.name,
        fileId: "",
        status: "uploading",
        message: "上传中..."
      }

      showAttachment.value.push(data)

      uploadAttachment(file)
    },
    fail: () => {
      showToast("选择视频失败")
    }
  })
}

// 上传附件
const uploadAttachment = (file: any) => {
  uploadWorkOrderAttachmentApi({ filePath: file.path })
    .then(res => {
      // 处理不同的响应格式
      let fileId = ""
      let fileName = ""

      if (res.data && res.data.data) {
        // 新版API返回格式
        fileId = res.data.data.fileId || ""
        fileName = res.data.data.originalname || file.name
      } else {
        // 旧版API返回格式
        fileId = res.fileId || ""
        fileName = res.filename || file.name
      }

      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.fileId = fileId
          item.status = "success"
          item.message = ""
        }
      })

      formData.value.attachments.push({
        fileId: fileId,
        fileName: fileName,
        fileType: file.type
      })
    })
    .catch(err => {
      console.log(err)
      showToast("上传失败")
      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.status = "error"
          item.message = "上传失败"
        }
      })
    })
}

// 删除图片
const deleteImage = async (file: any) => {
  try {
    if (file.fileId) {
      await deleteFile(file.fileId)
    }

    showAttachment.value = showAttachment.value.filter(
      item => (!item.fileId && item.tempPath !== file.tempPath) || (item.fileId && item.fileId !== file.fileId)
    )

    formData.value.attachments = formData.value.attachments.filter(item => item.fileId && item.fileId !== file.fileId)
  } catch (error) {
    showToast("删除失败")
  }
}

// 表单提交
const submitForm = async () => {
  const uploadingFiles = showAttachment.value.filter(file => file.status === "uploading")
  if (uploadingFiles.length > 0) return showToast("文件上传中，请等待文件上传后提交")
  // 表单验证
  if (!formData.value.detailLocation) {
    return showToast("请输入详细地址")
  }
  if (!formData.value.serviceClass) {
    return showToast("请选择报修类型")
  }
  if (!formData.value.faultDesc) {
    return showToast("请输入故障描述")
  }

  try {
    showLoading("保存中...")
    await editWorkOrderApi({
      workOrderId: workOrderId.value,
      ...formData.value
    })

    showToast("保存成功")

    // 刷新页面
    await getWorkOrderDetail()
    uni.$emit("repairOrderListRefresh")
  } catch (error: any) {
    showToast(error.message || "保存失败")
  } finally {
    hideLoading()
  }
}

// 联系报修人
const contactReporter = async () => {
  if (!workOrderDetail.value?.reporterPhone) {
    return showToast("无法获取报修人电话")
  }

  uni.showActionSheet({
    itemList: ["拨打电话", "复制号码"],
    title: workOrderDetail.value?.reporterPhone,
    success: res => {
      if (res.tapIndex === 0) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: workOrderDetail.value?.reporterPhone,
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none"
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 复制号码
        uni.setClipboardData({
          data: workOrderDetail.value?.reporterPhone || "",
          success: () => {
            uni.showToast({
              title: "电话号码已复制",
              icon: "none"
            })
          }
        })
      }
    }
  })
}

// 确认到达
const handleConfirmArrival = async () => {
  try {
    showLoading("获取位置信息中...", true)
    await confirmArrival({
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      location: "客户现场"
    })

    hideLoading()
    // 刷新页面
    getWorkOrderDetail()
    uni.$emit("repairOrderListRefresh")
    showToast("确认到达成功")

    // uni.getLocation({
    //   type: "wgs84", // 改用 wgs84 坐标系统
    //   success: async res => {
    //     hideLoading()
    //     uni.navigateTo({
    //       url: `/pages/order-detail/confirm-arrival?workOrderId=${workOrderId.value}&longitude=${res.longitude}&latitude=${res.latitude}`
    //     })
    //   },
    //   fail: err => {
    //     hideLoading()
    //     showToast("获取位置失败,请检查定位权限是否开启")
    //     console.error("获取位置失败", err.errMsg)
    //   }
    // })
  } catch (error: any) {
    hideLoading()
    showToast(error.errMsg || "获取位置失败")
  }
}

// 无需维修
const handleNoNeedRepair = () => {
  noNeedRepairPopup.value?.open()
}

// 关闭弹窗
const closeNoNeedRepairPopup = () => {
  noNeedRepairPopup.value?.close()
}

const closeCancelOrderPopup = () => {
  cancelOrderPopup.value?.close()
}

// 提交无需维修
const submitNoNeedRepair = async () => {
  if (!noNeedRepairReason.value) {
    showToast("请输入无需维修原因")
    return
  }

  try {
    showLoading("提交无需维修中...", true)
    await setMaintenanceFree({
      workOrderId: workOrderId.value,
      reason: noNeedRepairReason.value,
      maintenanceUnitId: userStore.unit
    })
    hideLoading()
    noNeedRepairPopup.value?.close()
    // 刷新页面
    getWorkOrderDetail()
    uni.$emit("repairOrderListRefresh")
    showToast("操作成功")
  } catch (error) {
    hideLoading()
    showToast("操作失败")
  }
}

// 处理与报价
const createRepairPlan = () => {
  uni.navigateTo({
    url: `/pages/order-detail/process-price?workOrderId=${workOrderId.value}&isModify=false`
  })
}

// 修改维修报价
const modifyRepairPlan = () => {
  uni.navigateTo({
    url: `/pages/order-detail/process-price?workOrderId=${workOrderId.value}&isModify=true`
  })
}

// 完成维修
const finishRepair = async () => {
  // 跳转到完成维修页面
  uni.navigateTo({
    url: `/pages/order-detail/finish-repair?workOrderId=${workOrderId.value}`
  })
}

// 格式化价格
const formatPrice = (price: number | string): string => {
  if (price === undefined || price === null) return "0.00"
  const numPrice = typeof price === "string" ? parseFloat(price) : price
  return numPrice.toFixed(2)
}

// 打开退单弹窗
const openCancelOrderPopup = () => {
  cancelOrderPopup.value?.open()
}

// 提交退单
const submitCancelOrder = async () => {
  if (!cancelOrderReason.value) {
    showToast("请输入退单原因")
    return
  }

  try {
    showLoading("退单中...", true)
    await returnOrder({
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      reason: cancelOrderReason.value
    })
    hideLoading()
    cancelOrderPopup.value?.close()
    getWorkOrderDetail()
    uni.$emit("repairOrderListRefresh")
    showToast("退单成功")
  } catch (error: any) {
    hideLoading()
    showToast(error.message || "退单失败")
  }
}

// 前往退单页面
const navigateToReturnOrder = () => {
  uni.navigateTo({
    url: `/pages/order-detail/return-order?workOrderId=${workOrderId.value}`
  })
}

// 派单
const dispatchOrder = () => {
  uni.navigateTo({
    url: `/pages/order-detail/dispatch-order?workOrderId=${workOrderId.value}`
  })
}

// 下拉刷新相关方法
const onPulling = () => {
  // 可以在这里处理下拉时的逻辑
}

const onRefresh = async () => {
  refresherTriggered.value = true

  // 刷新工单详情
  await getWorkOrderDetail()

  // 完成刷新
  refresherTriggered.value = false
}

const onRestore = () => {
  // 刷新还原
  refresherTriggered.value = false
}

const onAbort = () => {
  // 刷新中止
  refresherTriggered.value = false
}
</script>

<style lang="scss" scoped>
.repair-detail {
  background-color: #f5f5f5;
  height: calc(100vh - var(--window-top));
  width: 100%;

  .section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 24rpx;
      border-bottom: 1px solid #eee;
      padding-bottom: 16rpx;
    }
    .section-hint {
      font-size: 26rpx;
      color: #aaa;
      margin-top: 10rpx;
    }
  }

  .info-item {
    display: flex;
    margin-bottom: 16rpx;

    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #666;
    }

    .tag {
      width: fit-content;
      flex: none !important;
      &-success {
        color: #1890ff;
      }
      &-warning {
        color: #e69215;
      }
      &-error {
        color: #f56c6c;
      }
      &-info {
        color: #909399;
      }
    }

    .value {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      word-break: break-all;

      .call-btn {
        margin-left: 20rpx;
        color: $uni-color-primary;
      }
    }
  }

  .upload-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;

    .image-item,
    .upload-item {
      width: 200rpx;
      height: 200rpx;
      margin: 10rpx;
      border-radius: 8rpx;
      overflow: hidden;
      position: relative;
    }

    .image-item {
      background-color: #f0f0f0;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24rpx;
      }

      .file-name {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 22rpx;
        padding: 4rpx 8rpx;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .delete-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        z-index: 1;
      }
    }

    .upload-item {
      background-color: #f9f9f9;
      border: 1px dashed #ddd;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .upload-icon {
        font-size: 60rpx;
        color: #999;
        line-height: 1;
      }
    }
  }

  .form-item {
    margin-bottom: 30rpx;

    .label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;

      .required {
        color: #ff0000;
        margin-right: 4rpx;
      }
    }

    .input,
    .textarea,
    .picker-value {
      width: 100%;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
    }

    .input {
      height: 68rpx;
      line-height: 28rpx;
    }

    .textarea {
      height: 200rpx;
      width: 100%;
    }

    .picker-container {
      width: 100%;
      position: relative;
    }

    .picker-value {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &.placeholder {
        color: #999;
      }

      &.loading {
        color: #999;

        .loading-icon {
          animation: loading-rotate 1s linear infinite;
          margin-right: 8rpx;
        }
      }

      &.empty {
        color: #999;
        background-color: #f8f8f8;
      }
    }

    @keyframes loading-rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    .word-count {
      text-align: right;
      font-size: 24rpx;
      color: #999;
      margin-top: 8rpx;
    }
  }

  .timeline {
    padding: 20rpx 0;

    .timeline-item {
      position: relative;
      padding-left: 30rpx;
      margin-bottom: 30rpx;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 12rpx;
        width: 12rpx;
        height: 12rpx;
        background-color: $uni-color-primary;
        border-radius: 50%;
      }

      &:after {
        content: "";
        position: absolute;
        left: 5rpx;
        top: 24rpx;
        width: 2rpx;
        height: calc(100% + 10rpx);
        background-color: #eee;
      }

      &:last-child:after {
        display: none;
      }

      .time {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
      }

      .content-mark {
        margin-top: 10rpx;
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        background-color: #f5f5f5;
        padding: 20rpx;
        box-sizing: border-box;
        border-radius: 10rpx;
        .signature-base64 {
          display: flex;
          align-items: center;
          .signature-base64-title {
            margin-right: 10rpx;
            white-space: nowrap;
          }
          image {
            width: 120rpx;
            height: 60rpx;
            background-color: #fff;
            padding: 10rpx;
            border-radius: 10rpx;
          }
        }
        .attachment-container {
          display: flex;
          .attachment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12rpx;
            .attachment-item {
              width: 120rpx;
              .file-name {
                font-size: 24rpx;
                color: #666;
                margin-top: 8rpx;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }

  .price-list {
    margin-top: 20rpx;

    .price-item {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      padding: 16rpx;
      margin-bottom: 16rpx;

      .price-item-header {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .code {
          font-size: 24rpx;
          color: #999;
          margin-right: 16rpx;
        }

        .name {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
        }
      }

      .price-item-content {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #666;

        .desc {
          flex: 1;
        }

        .price {
          margin-left: 16rpx;
          color: #ff5a5f;
        }

        .quantity {
          margin-left: 16rpx;
          color: #333;
        }
      }
    }
  }

  .subsection {
    margin-top: 20rpx;
    margin-bottom: 20rpx;

    .subsection-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: bold;
    }
  }

  .service-items {
    .service-item {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .service-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-id {
          font-size: 26rpx;
          color: #666;
          font-weight: bold;
        }
      }

      .service-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-name {
          font-size: 28rpx;
          color: #333;
        }
      }

      .service-item-footer {
        display: flex;
        flex-direction: column;
        font-size: 26rpx;
        color: #666;
        .footer-row {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .footer-column {
            min-width: 50%;
            &:last-child {
              text-align: right;
            }
          }
        }
        .service-price {
          color: #f56c6c;
          white-space: nowrap;
        }

        .service-subtotal {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }

  .fee-statistics {
    margin-top: 30rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
      border-bottom: 1px solid #eee;
      padding-bottom: 10rpx;
    }

    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12rpx 0;
      border-bottom: 1rpx solid #eee;

      .fee-label {
        font-size: 28rpx;
        color: #666;
      }

      .fee-value {
        font-size: 28rpx;
        color: #333;
      }
    }

    .fee-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      margin-top: 10rpx;

      .fee-label {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
      }

      .fee-value {
        font-size: 30rpx;
        color: #f56c6c;
        font-weight: bold;

        &.total {
          font-size: 36rpx;
        }
      }
    }
  }

  .price-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    border-top: 1px solid #eee;
    font-size: 28rpx;

    .label {
      color: #666;
    }

    .price-total {
      color: #ff5a5f;
      font-weight: bold;
    }
  }

  .footer-buttons {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;

    .btn {
      margin-left: 20rpx;
      height: 80rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      border-radius: 40rpx;
      flex: 1;
      min-width: 0rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .btn-primary {
      background-color: $uni-color-primary;
      color: #fff;
    }

    .btn-default {
      background-color: #fff;
      color: #666;
      border: 1px solid #ddd;
    }
  }
}

// 自定义弹窗样式
.custom-popup {
  background-color: #fff;
  border-radius: 12rpx;
  width: 600rpx;
  overflow: hidden;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    padding: 30rpx;
    box-sizing: border-box;
    text-align: center;
    border-bottom: 1px solid #eee;
  }

  .popup-content {
    padding: 30rpx;
    box-sizing: border-box;

    textarea {
      width: 100%;
      height: 240rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 16rpx;
      box-sizing: border-box;
      font-size: 28rpx;
    }

    .picker {
      width: 100%;
      height: 80rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 0 16rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      line-height: 80rpx;
    }

    .form-item {
      margin-bottom: 20rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      input,
      textarea {
        width: 100%;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 16rpx;
        box-sizing: border-box;
        font-size: 28rpx;
      }

      textarea {
        height: 160rpx;
      }
    }
  }

  .popup-buttons {
    display: flex;
    border-top: 1px solid #eee;

    .popup-btn {
      flex: 1;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-size: 30rpx;
      border-radius: 0;
    }

    .popup-btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }

    .popup-btn-confirm {
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
}

.repair-plan-popup {
  width: 650rpx;
}

.bottom-placeholder {
  height: 120rpx;
}
</style>
