<template>
  <div class="layout-container">
    <div class="aside-container">
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        background-color="#2956AA"
        text-color="#fff"
        active-text-color="#ffffff"
        :collapse="isCollapse"
        router
        unique-opened
      >
        <template v-if="userStore.userType === UserTypeEnum.PLATFORM">
          <el-menu-item
            index="/home"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <HomeFilled />
            </el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-menu-item
            index="/workorder"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <Tickets />
            </el-icon>
            <span>工单管理</span>
          </el-menu-item>

          <el-sub-menu index="/maintenance-enterprise">
            <template #title>
              <div style="display: flex; align-items: center; width: 100%">
                <el-icon style="font-size: 18px">
                  <OfficeBuilding />
                </el-icon>
                <span>维保单位管理</span>
              </div>
            </template>
            <el-menu-item index="/maintenance-enterprise/admission">
              入驻管理
            </el-menu-item>
            <el-menu-item index="/maintenance-enterprise/staff">
              维保人员管理
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/customer">
            <template #title>
              <div style="display: flex; align-items: center; width: 100%">
                <el-icon style="font-size: 18px">
                  <User />
                </el-icon>
                <span>客户单位管理</span>
              </div>
            </template>
            <el-menu-item index="/customer/unit">客户单位管理</el-menu-item>
            <el-menu-item index="/customer/staff">报修人员管理</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/inventory">
            <template #title>
              <div style="display: flex; align-items: center; width: 100%">
                <el-icon style="font-size: 18px">
                  <Box />
                </el-icon>
                <span>物资库存管理</span>
              </div>
            </template>
            <el-menu-item index="/inventory/category">类别设置</el-menu-item>
            <el-menu-item index="/inventory/list">物资清单</el-menu-item>
            <el-menu-item index="/inventory/storage">物资库存</el-menu-item>
            <el-menu-item index="/inventory/inbound">入库</el-menu-item>
            <el-menu-item index="/inventory/outbound">出库</el-menu-item>
          </el-sub-menu>

          <el-menu-item
            index="/statistics"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <DataAnalysis />
            </el-icon>
            <span>统计管理</span>
          </el-menu-item>

          <el-menu-item
            index="/contract"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <Document />
            </el-icon>
            <span>合同管理</span>
          </el-menu-item>

          <el-sub-menu index="/settings">
            <template #title>
              <div style="display: flex; align-items: center; width: 100%">
                <el-icon style="font-size: 18px">
                  <Tools />
                </el-icon>
                <span>系统设置</span>
              </div>
            </template>
            <el-menu-item index="/settings/dictionary">字典配置</el-menu-item>
            <el-menu-item index="/settings/service-class">
              服务内容项目配置
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/user">
            <template #title>
              <div style="display: flex; align-items: center; width: 100%">
                <el-icon style="font-size: 18px">
                  <UserFilled />
                </el-icon>
                <span>用户管理</span>
              </div>
            </template>
            <el-menu-item index="/user/list">用户管理</el-menu-item>
            <el-menu-item index="/user/role">角色管理</el-menu-item>
            <el-menu-item index="/user/permission">权限管理</el-menu-item>
          </el-sub-menu>
        </template>

        <template v-if="userStore.userType === UserTypeEnum.MAINTENANCE_UNIT">
          <el-menu-item
            index="/home"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <HomeFilled />
            </el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-menu-item
            index="/workorder"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <Tickets />
            </el-icon>
            <span>维修工单明细</span>
          </el-menu-item>

          <el-menu-item
            index="/settlement"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <WalletFilled />
            </el-icon>
            <span>结算管理</span>
          </el-menu-item>
          <el-menu-item index="/maintenance-enterprise/staff">
            <el-icon style="font-size: 18px">
              <UserFilled />
            </el-icon>
            <span>用户管理</span>
          </el-menu-item>
        </template>

        <template v-if="userStore.userType === UserTypeEnum.REPAIR_UNIT">
          <el-menu-item
            index="/home"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <HomeFilled />
            </el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-menu-item
            index="/workorder"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <Tickets />
            </el-icon>
            <span>维修工单明细</span>
          </el-menu-item>

          <el-menu-item
            index="/settlement"
            style="display: flex; align-items: center"
          >
            <el-icon style="font-size: 18px">
              <WalletFilled />
            </el-icon>
            <span>结算管理</span>
          </el-menu-item>
          <el-menu-item index="/customer/staff">
            <el-icon style="font-size: 18px">
              <UserFilled />
            </el-icon>
            <span>用户管理</span>
          </el-menu-item>
        </template>
      </el-menu>
      <div class="collapse-btn" @click="toggleCollapse">
        <el-icon color="#fff">
          <Fold v-if="!isCollapse" />
          <Expand v-else />
        </el-icon>
      </div>
    </div>

    <el-container>
      <el-header class="header">
        <div class="left-section">
          <div class="logo">
            <span class="logo-text">
              即时修管理平台
              <span
                style="
                  font-size: 14px;
                  line-height: 50px;
                  vertical-align: bottom;
                "
              >
                ——测试版本
              </span>
            </span>
          </div>
        </div>
        <!-- <div class="header-center">
          <el-input placeholder="输入关键词" clearable class="search-input">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div> -->
        <div class="user-info">
          <!-- <el-badge :value="5" class="message-badge">
            <el-icon size="20">
              <Bell />
            </el-icon>
          </el-badge> -->

          <el-dropdown @command="handleCommand">
            <div class="el-dropdown-link">
              <el-avatar :size="32" :src="userAvatar" class="user-avatar" />
              <span class="welcome">{{ userStore.username || "Admin" }}</span>
              <el-icon>
                <CaretBottom />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-main class="main">
        <div class="main-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  HomeFilled,
  Tickets,
  User,
  Tools,
  OfficeBuilding,
  Box,
  DataAnalysis,
  Document,
  UserFilled,
  Bell,
  CaretBottom,
  Search,
  Fold,
  Expand,
  WalletFilled,
} from "@element-plus/icons-vue";
import { useUserStore } from "@/store";
import { UserTypeEnum } from "@/configs";
import { toastError } from "@/utils";
import { logout } from "@/api";

const router = useRouter();
const route = useRoute();

const userStore = useUserStore();

const userAvatar = ref(
  "https://cube.elemecdn.com/3/7c/********************************.png"
);
const isCollapse = ref(false);
const activeMenu = computed(() => route.path);

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
};

// 处理下拉菜单命令
const handleCommand = (command) => {
  if (command === "logout") {
    ElMessageBox.confirm("确定要退出登录吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        logout()
          .then(() => {
            userStore.clearUserInfo();
            ElMessage.success("已退出登录");
            router.push("/");
          })
          .catch((err) => {
            toastError(err);
          });
      })
      .catch(() => {});
  } else if (command === "profile") {
    handleProfile();
  }
};

onMounted(() => {
  // 初始化操作
});

const handleProfile = () => {
  router.push("/userinfo");
};
</script>

<style lang="less" scoped>
.layout-container {
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.aside-container {
  height: 100%;
  overflow: hidden;
  background-color: #2956aa;
  flex-shrink: 0;
  width: fit-content;
  display: flex;
  flex-direction: column;

  .collapse-btn {
    height: 56px;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #606266;
    border-radius: 4px;
    transition: all 0.3s;
    padding: 0px 20px;
    box-sizing: border-box;
  }
}

.header {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  width: 100%;
  box-sizing: border-box;

  .left-section {
    display: flex;
    align-items: center;

    .logo {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      align-items: center;
      height: 60px;
      overflow: hidden;
      white-space: nowrap;
      position: relative;

      &-text {
        color: #333;
        font-size: 20px;
        letter-spacing: 1px;
        margin-right: 20px;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    border-left: 1px solid #e5e5e5;
    transition: margin-left 0.3s;

    .search-input {
      width: 320px;

      :deep(.el-input__inner) {
        font-size: 14px;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 15px;

    .message-badge {
      cursor: pointer;
      position: relative;
      margin-right: 5px;

      :deep(.el-badge__content) {
        position: absolute;
        top: 12px;
        right: -10px;
        transform: scale(0.8);
        z-index: 1;
      }
    }

    .el-dropdown-link {
      display: flex;
      align-items: center;
      cursor: pointer;
      gap: 10px;
    }

    .user-avatar {
      margin-right: 8px;
    }

    .welcome {
      margin-right: 5px;
    }
  }
}

.el-menu-vertical {
  border-right: none;
  width: 200px;
  flex: 1;
  min-height: 0;
  overflow-y: auto;

  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    height: 56px;
    line-height: 56px;
    padding: 0 20px !important;
    font-size: 15px;
    position: relative;
  }

  &:not(.el-menu--collapse) {
    :deep(.el-sub-menu__title) {
      display: flex;
      align-items: center;

      .el-sub-menu__icon-arrow {
        position: absolute;
        right: 20px;
      }
    }
  }

  &.el-menu--collapse {
    width: 64px;

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      padding: 0 !important;
      text-align: center;
      justify-content: center;

      span {
        display: none;
      }
    }

    :deep(.el-icon) {
      margin: 0 auto !important;
    }

    :deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
      display: none;
    }
  }

  :deep(.el-menu-item span),
  :deep(.el-sub-menu__title span) {
    margin-left: 4px;
    letter-spacing: 0.5px;
    flex: 1;
  }

  :deep(.el-sub-menu__title) {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-icon:not(:first-child) {
      position: absolute;
      right: 20px;
      margin-right: 0;
    }
  }

  :deep(.el-menu-item) {
    display: flex;
    align-items: center;
  }

  :deep(.el-icon) {
    color: #ffffff;
    margin-right: 12px;
    font-size: 18px;
  }

  :deep(.el-menu--inline .el-menu-item) {
    padding-left: 54px !important;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }

  :deep(.el-sub-menu__title .el-icon:first-child),
  :deep(.el-menu-item .el-icon:first-child) {
    width: 18px;
    text-align: center;
    margin-right: 12px;
  }

  :deep(.el-menu-item.is-active) {
    background-color: #4373c5 !important;
    color: #ffffff !important;
  }

  :deep(.el-menu-item:hover),
  :deep(.el-sub-menu__title:hover) {
    background-color: #4373c5 !important;
  }

  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: #ffffff !important;
  }

  :deep(.el-menu--inline .el-menu-item) {
    padding-left: 40px !important;
  }
}

.main {
  background-color: #f5f7fa;
  padding: 20px;
  height: calc(100% - 60px);
  overflow: auto;
  box-sizing: border-box;
}
.main-content {
  min-height: 1000px;
  min-width: 960px;
  width: 100%;
  height: 100%;
}
</style>
