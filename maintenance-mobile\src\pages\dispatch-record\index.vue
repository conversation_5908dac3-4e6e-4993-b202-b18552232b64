<template>
  <view class="page-container">
    <NavBar title="派单记录" />
    <view class="page-content-with-nav repair-list">
      <!-- 列表为空提示 -->
      <view class="list-empty" v-if="dispatchList.length === 0">
        <image src="@/static/empty.png" mode="aspectFit" />
        <text>暂无派单记录</text>
      </view>

      <!-- 列表渲染 -->
      <scroll-view
        v-else
        class="repair-scroll"
        scroll-y
        @scrolltolower="onReachBottom"
        @refresherrefresh="onPullDownRefresh"
        refresher-enabled
        :refresher-triggered="refreshing"
        show-scrollbar="false">
        <view
          class="repair-item"
          v-for="(item, index) in dispatchList"
          :key="index"
          @click="goToRepairDetail(item.workOrderId)">
          <view class="repair-header">
            <view class="repair-type">{{ item.workOrderId }}</view>
            <view class="repair-status" :style="{ color: getStatusColor(item.status) }">{{
              getStatusText(item.status)
            }}</view>
          </view>
          <view class="repair-content">
            <view class="repair-address">{{ `地址：${item.detailLocation}` }}</view>
            <view class="repair-type-info">{{ `报修类型：${item.serviceClassLabel}` }}</view>
            <view class="repair-desc">{{ `故障描述：${item.faultDesc}` }}</view>
          </view>
          <view class="repair-footer">
            <view class="repair-time">{{ formatTime(item.reportTime) }}</view>
          </view>
        </view>

        <!-- 加载状态提示 -->
        <view class="loading-more" v-if="dispatchList.length > 0">
          <text v-if="hasMore && !refreshing">上拉加载更多</text>
          <text v-if="!hasMore">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { formatTime } from "@/utils"
import { NavBar } from "@/components"
import { getWorkOrderListApi } from "@/api/workorder"
import { useUserStore } from "@/store/user"
import { WorkOrderStatusEnum } from "@/configs"

const userStore = useUserStore()

const dispatchList = ref<any[]>([])
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const refreshing = ref(false)
const activeTab = ref("all")

// 判断用户是否为管理员或调度
const isAdminOrDispatcher = computed(() => {
  return userStore.roles.includes("管理员") || userStore.roles.includes("调度")
})

onLoad(() => {
  getDispatchList()
  uni.$on("repairOrderListRefresh", resetRefresh)
})

onUnload(() => {
  uni.$off("repairOrderListRefresh", resetRefresh)
})

function resetRefresh() {
  pageNum.value = 1
  hasMore.value = true
  dispatchList.value = []
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })
  getDispatchList()
}

// 下拉刷新
const onPullDownRefresh = () => {
  refreshing.value = true
  pageNum.value = 1
  hasMore.value = true
  dispatchList.value = []
  getDispatchList()
    .then(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
    .catch(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
}

// 上拉加载更多
const onReachBottom = () => {
  if (hasMore.value && !refreshing.value) {
    pageNum.value++
    getDispatchList()
  }
}

// 获取派单列表
const getDispatchList = async () => {
  try {
    if (!isAdminOrDispatcher.value) {
      uni.showToast({
        title: "您没有查看派单记录的权限",
        icon: "none"
      })
      return Promise.resolve()
    }

    const filters = `maintenanceUnitId=${userStore.unit},status=${WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH}`

    const params: any = {
      offset: (pageNum.value - 1) * pageSize.value,
      limit: pageSize.value,
      filters
    }

    const res = await getWorkOrderListApi(params)
    const data = res.data.data
    if (pageNum.value === 1) {
      dispatchList.value = data.rows
    } else {
      dispatchList.value = [...dispatchList.value, ...data.rows]
    }
    hasMore.value = data.rows.length === pageSize.value
    return Promise.resolve()
  } catch (error) {
    uni.showToast({
      title: "获取派单列表失败",
      icon: "none"
    })
    return Promise.reject(error)
  }
}

// 跳转到工单详情页
const goToRepairDetail = (workOrderId?: string) => {
  if (!workOrderId) return
  uni.navigateTo({
    url: `/pages/order-detail/index?workOrderId=${workOrderId}`
  })
}

// 处理派单操作
const handleDispatch = (workOrderId?: string) => {
  if (!workOrderId) return
  uni.navigateTo({
    url: `/pages/dispatch/index?workOrderId=${workOrderId}`
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH:
      return "待派单"
    case WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME:
      return "待上门"
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN:
      return "待出具维修方案"
    case WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT:
      return "待平台审核"
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY:
      return "待修改维修方案"
    case WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM:
      return "待维修确认"
    case WorkOrderStatusEnum.PROCESSING:
      return "维修中"
    case WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM:
      return "待完成确认"
    case WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM:
      return "待平台确认"
    case WorkOrderStatusEnum.FINISHED:
      return "已完成"
    case WorkOrderStatusEnum.CANCELLED:
      return "已取消"
    case WorkOrderStatusEnum.NO_NEED_REPAIR:
      return "无需维修"
    default:
      return "未知状态"
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH:
      return "#ff6600" // 派单状态颜色突出
    case WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME:
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN:
    case WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT:
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY:
    case WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM:
    case WorkOrderStatusEnum.PROCESSING:
    case WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM:
    case WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM:
      return "#ff9900"
    case WorkOrderStatusEnum.FINISHED:
      return "#4cd964"
    case WorkOrderStatusEnum.CANCELLED:
    case WorkOrderStatusEnum.NO_NEED_REPAIR:
      return "#8f8f8f"
    default:
      return "#333"
  }
}
</script>

<style lang="scss">
.repair-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0rpx;

  .repair-scroll {
    flex: 1;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .repair-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .repair-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      padding-bottom: 16rpx;
      box-sizing: border-box;
      margin-bottom: 16rpx;

      .repair-type {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }

      .repair-status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        box-sizing: border-box;
        border-radius: 6rpx;
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .repair-content {
      margin-bottom: 16rpx;

      .repair-address,
      .repair-type-info,
      .repair-desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .repair-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16rpx;
      box-sizing: border-box;
      border-top: 1px solid #eee;

      .repair-time {
        font-size: 24rpx;
        color: #999;
      }

      .repair-actions {
        .action-btn {
          background-color: #ff6600;
          color: #fff;
          font-size: 24rpx;
          padding: 6rpx 20rpx;
          box-sizing: border-box;
          border-radius: 30rpx;
          line-height: 1.5;
        }
      }
    }
  }

  .list-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
    flex: 1;
    min-height: 0rpx;
    gap: 20rpx;
    margin-top: -100rpx;
    image {
      width: 300rpx;
      height: 300rpx;
      object-fit: contain;
    }
  }

  .loading-more {
    text-align: center;
    padding: 20rpx 0;
    box-sizing: border-box;
    color: #999;
    font-size: 24rpx;
  }
}
</style>
