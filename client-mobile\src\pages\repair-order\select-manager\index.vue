<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="选择报修管理人员" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav select-manager">
      <!-- 搜索框 -->
      <view class="search-box">
        <view class="search-input">
          <input type="text" v-model="searchText" placeholder="搜索" @confirm="onSearchInput" />
          <view class="search-icon">
            <uni-icons v-if="searchText" type="closeempty" size="18" color="#999" @click="onSearchInput"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 人员列表 -->
      <scroll-view
        scroll-y
        class="manager-list"
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="onLoadMore">
        <view class="manager-item" v-for="item in staffList" :key="item.id" @click="selectManager(item)">
          <view class="manager-info">
            <view class="name"
              >{{ item.username }} <text class="id">({{ item.phone }})</text></view
            >
          </view>
        </view>
        <view class="loading-more" v-if="isLoadingMore">加载中...</view>
        <view class="no-more" v-if="noMoreData && staffList.length > 0">没有更多数据了</view>
        <view class="no-more" v-if="staffList.length > 0 && !noMoreData">上拉加载更多</view>
        <view class="empty-data" v-if="staffList.length === 0 && !isLoadingMore">
          <text class="empty-text">暂无数据</text>
        </view>
      </scroll-view>

      <!-- 确认提示弹窗 -->
      <uni-popup ref="confirmPopup" type="dialog">
        <uni-popup-dialog
          title="确认"
          :content="confirmContent"
          :before-close="true"
          @confirm="confirmSelection"
          @close="() => $refs.confirmPopup.close()"></uni-popup-dialog>
      </uni-popup>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue"
import { NavBar } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import { getCustomerUnitStaffList } from "@/api"
import { showToast, showLoading, hideLoading } from "@/utils"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()
const workOrderId = ref("")
const reportUnitId = ref("")

// 搜索相关
const searchText = ref("")

// 列表相关
const staffList = ref([])
const pageNum = ref(1)
const pageSize = ref(14)
const total = ref(0)
const isLoadingMore = ref(false)
const isRefreshing = ref(false)
const noMoreData = computed(() => {
  return staffList.value.length >= total.value
})

// 选择相关
const selectedManager = ref(null)
const confirmPopup = ref(null)
const confirmContent = computed(() => {
  if (!selectedManager.value) return ""

  return `确定要将交给【${selectedManager.value.username || selectedManager.value.name}】处理吗?`
})

onLoad(options => {
  workOrderId.value = options.workOrderId

  // 直接使用当前用户的单位ID
  reportUnitId.value = userStore.unit
  fetchStaffList(true)
})

const clearSearch = () => {
  searchText.value = ""
  fetchStaffList(true)
}

// 搜索输入事件
const onSearchInput = () => {
  pageNum.value = 1
  fetchStaffList(true)
}

// 获取员工列表
const fetchStaffList = async (refresh = false) => {
  try {
    if (refresh) {
      isRefreshing.value = true
      pageNum.value = 1
    } else {
      isLoadingMore.value = true
    }

    const params = {
      limit: pageSize.value,
      offset: (pageNum.value - 1) * pageSize.value,
      unitId: reportUnitId.value,
      roles: ["管理员", "报修审核员"],
      showMask: "false"
    }
    params.filters = `username=${searchText.value}`

    const res = await getCustomerUnitStaffList(params)
    // 后端接口返回格式处理
    const data = res.data.data || {}
    const list = data?.rows || []
    const totalCount = data?.pageElements?.totalElements || 0

    total.value = totalCount

    if (refresh) {
      staffList.value = list
    } else {
      staffList.value = [...staffList.value, ...list]
    }
  } catch (error) {
    showToast(error.message || "获取列表失败")
  } finally {
    isRefreshing.value = false
    isLoadingMore.value = false
  }
}

// 下拉刷新
const onRefresh = () => {
  fetchStaffList(true)
}

// 加载更多
const onLoadMore = () => {
  console.log(noMoreData.value, isLoadingMore.value)
  if (noMoreData.value || isLoadingMore.value) return
  pageNum.value++
  fetchStaffList(false)
}

// 选择管理人员
const selectManager = manager => {
  selectedManager.value = manager
  confirmPopup.value.open()
}

// 确认选择
const confirmSelection = async () => {
  if (!selectedManager.value) return

  try {
    // 使用uni.$emit触发工单详情刷新事件
    uni.$emit("selectManager", {
      workOrderId: workOrderId.value,
      selectedManager: {
        userId: selectedManager.value.userId,
        username: selectedManager.value.username,
        phone: selectedManager.value.phone
      }
    })
    uni.navigateBack({
      delta: 1
    })
  } catch (error) {
    showToast(error.message || "确认失败")
  } finally {
    hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.select-manager {
  .search-box {
    padding: 20rpx;
    background-color: #fff;

    .search-input {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 30rpx;
      padding: 10rpx 20rpx;

      .search-icon {
        margin-right: 10rpx;
        color: #999;
      }

      input {
        flex: 1;
        height: 60rpx;
        font-size: 28rpx;
      }
    }
  }

  .manager-list {
    height: calc(100% - 130rpx);
    background-color: #f5f5f5;

    .manager-item {
      background-color: #fff;
      margin-bottom: 2rpx;
      padding: 30rpx 20rpx;

      .manager-info {
        .name {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 10rpx;

          .id {
            font-size: 28rpx;
            color: #999;
          }
        }

        .phone {
          font-size: 28rpx;
          color: #666;
        }
      }

      &:active {
        background-color: #f0f0f0;
      }
    }

    .loading-more,
    .no-more,
    .empty-data {
      text-align: center;
      padding: 30rpx;
      color: #999;
      font-size: 28rpx;
    }

    .empty-data {
      padding: 100rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      .empty-text {
        margin-top: 20rpx;
      }
    }
  }
}

.page-content-with-nav {
  height: calc(100% - var(--status-bar-height) - 88rpx);
  box-sizing: border-box;
}
</style>
