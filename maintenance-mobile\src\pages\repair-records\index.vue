<template>
  <view class="page-container">
    <NavBar title="报修记录" />
    <view class="page-content-with-nav repair-list">
      <!-- 添加顶部标签导航 -->
      <view class="tab-container">
        <view class="tab-item" :class="{ active: activeTab === 'all' }" @click="switchTab('all')">全部</view>
        <view class="tab-item" :class="{ active: activeTab === 'progress' }" @click="switchTab('progress')"
          >进行中</view
        >
        <view class="tab-item" :class="{ active: activeTab === 'finished' }" @click="switchTab('finished')"
          >已完成</view
        >
      </view>

      <!-- 列表为空提示 -->
      <view class="list-empty" v-if="repairList.length === 0">
        <image src="@/static/empty.png" mode="aspectFit" />
        <text>暂无报修记录</text>
      </view>

      <!-- 列表渲染 -->
      <scroll-view
        v-else
        class="repair-scroll"
        scroll-y
        @scrolltolower="onReachBottom"
        @refresherrefresh="onPullDownRefresh"
        refresher-enabled
        :refresher-triggered="refreshing"
        show-scrollbar="false">
        <view
          class="repair-item"
          v-for="(item, index) in repairList"
          :key="index"
          @click="goToRepairDetail(item.workOrderId)">
          <view class="repair-header">
            <view class="repair-type">{{ item.workOrderId }}</view>
            <view class="repair-status" :style="{ color: getStatusColor(item.status) }">{{
              getStatusText(item.status)
            }}</view>
          </view>
          <view class="repair-content">
            <view class="repair-address">{{ `地址：${item.detailLocation}` }}</view>
            <view class="repair-type-info">{{ `报修类型：${item.serviceClassLabel}` }}</view>
            <view class="repair-desc">{{ `故障描述：${item.faultDesc}` }}</view>
          </view>
          <view class="repair-footer">
            <view class="repair-time">{{ formatTime(item.reportTime) }}</view>
          </view>
        </view>

        <!-- 加载状态提示 -->
        <view class="loading-more" v-if="repairList.length > 0">
          <text v-if="hasMore && !refreshing">上拉加载更多</text>
          <text v-if="!hasMore">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { formatTime } from "@/utils"
import { NavBar } from "@/components"
import { getWorkOrderListApi } from "@/api/workorder"
import { useUserStore } from "@/store/user"
import { WorkOrderStatusEnum } from "@/configs"

const userStore = useUserStore()

const repairList = ref<any[]>([])
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const refreshing = ref(false)
const activeTab = ref("all")

// 根据Tab获取对应的工单状态
const getStatusByTab = computed(() => {
  switch (activeTab.value) {
    case "progress":
      return "WAITING_DISPATCH,WAITING_COMPANY_DISPATCH,WAITING_REPAIR_PERSON_COME,WAITING_REPAIR_PLAN,WAITING_PLATFORM_AUDIT,WAITING_REPAIR_PLAN_MODIFY,WAITING_REPAIR_CONFIRM,PROCESSING,WAITING_REPAIR_FINISH_CONFIRM,WAITING_PLATFORM_FINISH_CONFIRM"
    case "finished":
      return "FINISHED,CANCELLED,NO_NEED_REPAIR"
    default:
      return ""
  }
})

onLoad(() => {
  uni.$on("repairOrderListRefresh", resetRefresh)
  getRepairList()
})

onUnload(() => {
  uni.$off("repairOrderListRefresh", resetRefresh)
})

// 切换标签
const switchTab = (tab: string) => {
  if (activeTab.value === tab) return
  activeTab.value = tab
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  getRepairList()
}

function resetRefresh() {
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })
  getRepairList()
}

// 下拉刷新
const onPullDownRefresh = () => {
  refreshing.value = true
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  getRepairList()
    .then(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
    .catch(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
}

// 上拉加载更多
const onReachBottom = () => {
  if (hasMore.value && !refreshing.value) {
    pageNum.value++
    getRepairList()
  }
}

// 获取工单列表
const getRepairList = async () => {
  try {
    let filters = `creatorUnitId=${userStore.unit},creatorId=${userStore.userId}`
    // 构建查询参数
    const params: any = {
      offset: (pageNum.value - 1) * pageSize.value,
      limit: pageSize.value,
      filters
    }

    // 根据选项卡设置状态过滤
    if (activeTab.value !== "all") {
      params.commonStatus = getStatusByTab.value
    }

    const res = await getWorkOrderListApi(params)
    const data = res.data.data
    if (pageNum.value === 1) {
      repairList.value = data.rows
    } else {
      repairList.value = [...repairList.value, ...data.rows]
    }
    hasMore.value = data.rows.length === pageSize.value
  } catch (error) {
    uni.showToast({
      title: "获取报修记录失败",
      icon: "none"
    })
  }
}

// 跳转到工单详情页
const goToRepairDetail = (workOrderId?: string) => {
  if (!workOrderId) return
  uni.navigateTo({
    url: `/pages/order-detail/index?workOrderId=${workOrderId}`
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case WorkOrderStatusEnum.WAITING_DISPATCH:
      return "待派单"
    case WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH:
      return "待接单"
    case WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME:
      return "待上门"
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN:
      return "待出具维修方案"
    case WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT:
      return "待平台审核"
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY:
      return "待修改维修方案"
    case WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM:
      return "待维修确认"
    case WorkOrderStatusEnum.PROCESSING:
      return "维修中"
    case WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM:
      return "待完成确认"
    case WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM:
      return "待平台确认"
    case WorkOrderStatusEnum.FINISHED:
      return "已完成"
    case WorkOrderStatusEnum.CANCELLED:
      return "已取消"
    case WorkOrderStatusEnum.NO_NEED_REPAIR:
      return "无需维修"
    default:
      return "未知状态"
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case WorkOrderStatusEnum.WAITING_DISPATCH:
    case WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH:
    case WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME:
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN:
    case WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT:
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY:
    case WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM:
    case WorkOrderStatusEnum.PROCESSING:
    case WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM:
    case WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM:
      return "#ff9900"
    case WorkOrderStatusEnum.FINISHED:
      return "#4cd964"
    case WorkOrderStatusEnum.CANCELLED:
    case WorkOrderStatusEnum.NO_NEED_REPAIR:
      return "#8f8f8f"
    default:
      return "#333"
  }
}
</script>

<style lang="scss">
.repair-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0rpx;

  .tab-container {
    display: flex;
    justify-content: space-around;
    background-color: #fff;
    padding: 24rpx 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 10;

    .tab-item {
      font-size: 28rpx;
      color: #666;
      padding: 10rpx 20rpx;
      position: relative;

      &.active {
        color: $uni-color-primary;
        font-weight: bold;

        &:after {
          content: "";
          position: absolute;
          bottom: -8rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: $uni-color-primary;
          border-radius: 2rpx;
        }
      }
    }
  }

  .repair-scroll {
    flex: 1;
    min-height: 0px;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .repair-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .repair-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      padding-bottom: 16rpx;
      box-sizing: border-box;
      margin-bottom: 16rpx;

      .repair-type {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }

      .repair-status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        box-sizing: border-box;
        border-radius: 6rpx;
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .repair-content {
      margin-bottom: 16rpx;

      .repair-address,
      .repair-type-info,
      .repair-desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .repair-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16rpx;
      box-sizing: border-box;
      border-top: 1px solid #eee;

      .repair-time {
        font-size: 24rpx;
        color: #999;
      }

      .repair-actions {
        .action-btn {
          background-color: #3c9cff;
          color: #fff;
          font-size: 24rpx;
          padding: 6rpx 20rpx;
          box-sizing: border-box;
          border-radius: 30rpx;
          line-height: 1.5;
        }
      }
    }
  }

  .list-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
    flex: 1;
    min-height: 0rpx;
    gap: 20rpx;
    margin-top: -100rpx;
    image {
      width: 300rpx;
      height: 300rpx;
      object-fit: contain;
    }
  }

  .loading-more {
    text-align: center;
    padding: 20rpx 0;
    box-sizing: border-box;
    color: #999;
    font-size: 24rpx;
  }
}
</style>
