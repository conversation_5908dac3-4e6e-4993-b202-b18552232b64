import wxJs from "weixin-js-sdk"
import moment from "moment"

export const showLoading = (title: string, mask = true) => {
  return uni.showLoading({ title: title, mask: mask })
}

interface ToastOptions {
  duration?: number
  icon?: "success" | "loading" | "error" | "none"
  mask?: boolean
}

export const showToast = (title: string, options?: ToastOptions) => {
  return uni.showToast({
    title: title,
    duration: options?.duration || 2500,
    icon: options?.icon || "none",
    mask: options?.mask || false
  })
}

export const hideLoading = () => {
  uni.hideLoading()
}

export const wxReLaunch = (url: string) => {
  // #ifdef H5
  // @ts-ignore
  if (window.__wxjs_environment === "miniprogram") {
    wxJs.miniProgram.reLaunch({ url: url })
  } else {
    uni.reLaunch({ url: url })
  }
  // #endif
  uni.reLaunch({ url: url })
}

export const throttle = (func: any, time: number) => {
  let start: number = Date.now()
  return function () {
    const end: number = Date.now()
    if (end - start >= time) {
      // @ts-ignore
      func.apply(this, arguments)
      start = Date.now()
    }
  }
}

export const debounce = (func: any, time: number) => {
  let timer
  return function () {
    clearTimeout(timer)
    timer = setTimeout(() => {
      // @ts-ignore
      func.apply(this, arguments)
    }, time)
  }
}

/* =============== time =============== */

export const formatTime = (date, template: string = "YYYY-MM-DD HH:mm:ss") => {
  return date ? moment(date).format(template) : "--"
}

// 获取一天中00:00:00的时间戳
export const getDayStartTime = (date: Date) => {
  date.setHours(0)
  date.setMinutes(0)
  date.setSeconds(0)
  return date.getTime()
}

// 获取一天中23:59:59的时间戳
export const getDayEndTime = (date: Date) => {
  date.setHours(23)
  date.setMinutes(59)
  date.setSeconds(59)
  return date.getTime()
}

// 睡眠延时
export const sleep = (time: number) => {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}
