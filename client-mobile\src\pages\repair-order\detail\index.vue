<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="报修详情" />

    <!-- 页面内容 -->
    <scroll-view
      class="page-content-with-nav repair-detail"
      scroll-y
      refresher-enabled
      :refresher-triggered="refresherTriggered"
      @refresherrefresh="onRefresh">
      <!-- 基本信息 -->
      <form @submit="submitForm">
        <view class="section">
          <view class="section-title">基本信息</view>
          <view v-if="!isNew" class="info-item">
            <view class="label">工单号</view>
            <view class="value">{{ workOrderDetail?.workOrderId }}</view>
          </view>
          <view v-if="!isNew" class="info-item">
            <view class="label">工单状态</view>
            <view class="value tag" :class="`tag-${showStatus.type}`">{{ showStatus.label }}</view>
          </view>
          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"><span v-if="isEditable" class="required">*</span> 详细地址 </view>
            <input
              v-if="isEditable"
              name="detailLocation"
              v-model="formData.detailLocation"
              placeholder="请输入详细地址" />
            <view v-else class="value">{{ formData.detailLocation || "--" }}</view>
          </view>
          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"> <span v-if="isEditable" class="required">*</span>报修类型 </view>
            <picker
              v-if="isEditable"
              name="serviceClass"
              :value="repairTypeIndex"
              :range="repairTypes"
              range-key="serviceClass"
              @change="onRepairTypeChange"
              :disabled="repairTypesLoading || repairTypes.length === 0">
              <view
                class="picker-value"
                :class="{ loading: repairTypesLoading, empty: repairTypes.length === 0 }"
                @click="
                  e => {
                    if (repairTypes.length === 0) showToast('暂无报修类型数据')
                  }
                ">
                <view>
                  <uni-icons
                    v-if="repairTypesLoading"
                    type="spinner-cycle"
                    size="16"
                    color="#999"
                    class="loading-icon"></uni-icons>
                  <text v-if="repairTypesLoading">加载中...</text>
                  <text v-else>{{
                    typeof repairTypeIndex === "number" ? repairTypes[repairTypeIndex].serviceClass : "请选择"
                  }}</text>
                </view>
                <uni-icons
                  type="right"
                  size="16"
                  color="#999"
                  v-if="!repairTypesLoading && repairTypes.length > 0"></uni-icons>
              </view>
            </picker>

            <view v-else class="value">{{ repairTypeLabel || "--" }}</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label"><span v-if="isEditable" class="required">*</span>故障描述 </view>
            <textarea v-if="isEditable" name="faultDesc" v-model="formData.faultDesc" placeholder="请输入故障描述" />
            <view v-else class="value">{{ formData.faultDesc || "--" }}</view>
          </view>

          <view :class="{ 'form-item': isEditable, 'info-item': !isEditable }">
            <view class="label">机器信息</view>
            <textarea v-if="isEditable" name="deviceInfo" v-model="formData.deviceInfo" placeholder="请输入机器信息" />
            <view v-else class="value">{{ formData.deviceInfo || "--" }}</view>
          </view>
          <view v-if="!isNew" class="info-item">
            <view class="label">报修时间</view>
            <view class="value">{{ formatTime(workOrderDetail?.reportTime) || "--" }}</view>
          </view>
        </view>

        <!-- 附件信息 -->
        <view v-if="showAttachment.length > 0 || isEditable" class="section">
          <view class="section-title">附件信息</view>
          <view v-if="isEditable" class="section-hint">请注意上传的数据中是否包含敏感信息</view>
          <view class="upload-list">
            <view class="image-item" v-for="(item, index) in showAttachment" :key="index">
              <template v-if="isImage(item.fileType)">
                <BlobImage
                  v-if="item.status === 'success'"
                  style="width: 200rpx; height: 200rpx"
                  :file-id="item.fileId" />
                <image v-else :src="item.tempPath" style="width: 200rpx; height: 200rpx" mode="aspectFit"></image>
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>
              <template v-else-if="isVideo(item.fileType)">
                <BlobVideo style="width: 200rpx; height: 200rpx" :file-id="item.fileId" />
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>

              <view class="file-name">{{ item.fileName }}</view>
              <text v-if="isEditable" class="delete-icon" @click.stop="deleteImage(item)">×</text>
            </view>
            <view class="upload-item" v-if="isEditable" @click="chooseAttachment">
              <text class="upload-icon">+</text>
            </view>
          </view>
        </view>

        <!-- 维修方案与价格 -->
        <view class="section" v-if="!isNew && (workOrderDetail?.processingOpinion || workOrderDetail?.totalPrice)">
          <view class="section-title">维修方案与价格</view>

          <!-- 处理意见 -->
          <view class="info-item">
            <view class="label">结算方式</view>
            <view class="value">
              {{ getSettlementType(workOrderDetail?.serviceItems?.[0]?.reportUnitSettlementType) || "--" }}
            </view>
          </view>

          <!-- 处理意见 -->
          <view class="info-item">
            <view class="label">处理意见</view>
            <view class="value">{{ workOrderDetail.processingOpinion || "暂无处理意见" }}</view>
          </view>

          <!-- 交通距离 -->
          <view class="info-item" v-if="workOrderDetail.distance || workOrderDetail.distance === 0">
            <view class="label">交通距离</view>
            <view class="value">{{ workOrderDetail.distance }} KM</view>
          </view>

          <!-- 服务类别与项目 -->
          <view class="subsection" v-if="workOrderDetail.serviceItems && workOrderDetail.serviceItems.length > 0">
            <view class="subsection-title">服务类别与项目</view>
            <view class="service-items">
              <view class="service-item" v-for="(item, index) in workOrderDetail.serviceItems" :key="index">
                <view class="service-item-header">
                  <view class="service-id">{{ item.code }}</view>
                </view>
                <view class="service-item-content">
                  <view class="service-name">{{ item.serviceItem }}</view>
                </view>
                <view class="service-item-footer">
                  <view class="footer-row">
                    <view class="service-price footer-column">单价：¥ {{ formatPrice(item.unitPrice) }}</view>
                    <view class="service-unit footer-column">单位：{{ item.unit }}</view>
                  </view>
                  <view class="footer-row">
                    <view class="service-quantity footer-column">数量：{{ item.quantity }}</view>
                    <view class="service-subtotal footer-column">小计：¥ {{ formatPrice(item.subtotal) }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 配件更换明细 -->
          <view class="subsection" v-if="workOrderDetail.parts && workOrderDetail.parts.length > 0">
            <view class="subsection-title">配件更换明细</view>
            <view class="service-items">
              <view class="service-item" v-for="(item, index) in workOrderDetail.parts" :key="index">
                <view class="service-item-header">
                  <view class="service-id">{{ item.id }}</view>
                </view>
                <view class="service-item-content">
                  <view class="service-name">{{ item.name }}</view>
                </view>
                <view class="service-item-footer">
                  <view class="footer-row" v-if="item.specification">
                    <view class="footer-column">规格：{{ item.specification }}</view>
                  </view>
                  <view class="footer-row">
                    <view class="service-price footer-column">单价：¥ {{ formatPrice(item.sellingPrice) }}</view>
                    <view class="service-unit footer-column" v-if="item.unit">单位：{{ item.unit }}</view>
                  </view>
                  <view class="footer-row" v-if="item.quantity">
                    <view class="service-quantity footer-column">数量：{{ item.quantity }}</view>
                    <view class="service-subtotal footer-column"
                      >小计：¥ {{ formatPrice(item.sellingPrice * (item.quantity || 1)) }}</view
                    >
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 费用统计 -->
          <view class="fee-statistics">
            <view class="fee-title">费用统计</view>
            <view class="fee-item" v-if="workOrderDetail.serviceFee !== undefined">
              <view class="fee-label">服务费</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.serviceFee) }}</view>
            </view>
            <view class="fee-item" v-if="workOrderDetail.transportFee !== undefined">
              <view class="fee-label">交通服务费 (超出50KM，¥ 2.00/KM)</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.transportFee) }}</view>
            </view>
            <view class="fee-item" v-if="workOrderDetail.partsTotal !== undefined">
              <view class="fee-label">配件价格合计</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.partsTotal) }}</view>
            </view>
            <view class="fee-total">
              <view class="fee-label">合计</view>
              <view class="fee-value">¥ {{ formatPrice(workOrderDetail.totalPrice) }}</view>
            </view>
          </view>
        </view>

        <!-- 处理信息 -->
        <view class="section" v-if="!isNew">
          <view class="section-title">处理信息</view>
          <view class="timeline">
            <view class="timeline-item" v-for="(item, index) in workOrderDetail?.repairProcess" :key="index">
              <view class="time">{{ `${item.processorName} ${formatTime(item.processTime)}` }}</view>
              <view class="content">{{ `${item.processType} ${item.processResult}` }}</view>
              <view v-if="showContentMark(item)" class="content-mark">
                <view v-if="item.location" class="location">{{ `地点：${item.location}` }}</view>
                <view v-if="item.reason" class="reason">{{ `原因：${item.reason}` }}</view>
                <view v-if="item.remark" class="remark">{{ `备注：${item.remark}` }}</view>
                <view v-if="item.repairResult" class="result">{{ `维修结果：${item.repairResult}` }}</view>
                <view v-if="item.evaluation" class="evaluation">{{ `评价：${item.evaluation}` }}</view>
                <view v-if="item.repairScore" class="repair-score">{{ `评分：${item.repairScore}` }}</view>
                <view v-if="item.signatureBase64" class="signature-base64">
                  <view class="signature-base64-title">签名：</view>
                  <image :src="item.signatureBase64" mode="aspectFit"></image>
                </view>
                <!-- 附件信息 -->
                <view v-if="item.attachments && item.attachments.length > 0" class="attachment-container">
                  <view class="attachment-title">附件：</view>
                  <view class="attachment-list">
                    <view class="attachment-item" v-for="(attachment, index) in item.attachments" :key="index">
                      <template v-if="isImage(attachment.fileType)">
                        <BlobImage style="width: 120rpx; height: 120rpx" :file-id="attachment.fileId" />
                      </template>
                      <BlobVideo
                        v-else-if="isVideo(attachment.fileType)"
                        style="width: 120rpx; height: 120rpx"
                        :file-id="attachment.fileId" />
                      <view class="file-name">{{ attachment.fileName }}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部占位 -->
        <view class="bottom-placeholder"></view>

        <!-- 底部按钮 -->
        <view v-if="showFooterButtons" class="fixed-bottom footer-buttons">
          <button v-if="isNew || isEditable" class="btn btn-primary" form-type="submit">提交</button>
          <view v-if="canCancel" class="btn btn-default" @click="confirmCancel">取消工单</view>
          <view v-if="canContactRepairPerson" class="btn btn-default" @click="contactRepairPerson">联系维保人员</view>
          <view v-if="canContactManager" class="btn btn-default" @click="contactManager">联系报修管理人员</view>
          <view v-if="canConfirmPlan" class="btn btn-primary" @click="confirmPlan">确认维修方案</view>
          <view v-if="canConfirmFinished" class="btn btn-primary" @click="confirmFinished">确认已完成</view>
        </view>
      </form>
    </scroll-view>

    <!-- 取消确认弹窗 -->
    <uni-popup ref="cancelPopup" type="dialog">
      <uni-popup-dialog
        title="取消工单"
        content="取消工单表示不需要维修，是否确认取消？"
        :before-close="true"
        @confirm="handleCancel"
        @close="() => $refs.cancelPopup.close()"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from "vue"
import { NavBar, BlobImage, BlobVideo } from "@/components"
import { onLoad, onUnload, onPullDownRefresh } from "@dcloudio/uni-app"
import {
  getWorkOrderDetailApi,
  getServiceContentApi,
  createWorkOrderApi,
  editWorkOrderApi,
  uploadWorkOrderAttachmentApi,
  deleteFile,
  repairConfirmApi,
  cancelWorkOrderApi,
  selectRepairManagerConfirmApi,
  selectFinishManagerConfirmApi
} from "@/api"
import { WorkOrderStatusEnum, ReportWayEnum } from "@/configs"
import { getWorkOrderStatus, formatTime, showToast, showLoading, hideLoading, getSettlementType } from "@/utils"
import { useUserStore } from "@/store/user"
import cloneDeep from "lodash/cloneDeep"

const userStore = useUserStore()

const workOrderId = ref("")
const isNew = ref(false)
const isEditable = ref(true)
const workOrderDetail = ref()
const formData = ref({
  detailLocation: "",
  serviceClass: "",
  faultDesc: "",
  attachments: [],
  deviceInfo: "",
  reportUnitId: "",
  reporterId: "",
  reporterPhone: "",
  reportWay: ReportWayEnum.SELF_REPORT
})

// 格式化价格
const formatPrice = price => {
  if (price === undefined || price === null) return "0.00"
  const numPrice = typeof price === "string" ? parseFloat(price) : price
  return numPrice.toFixed(2)
}

const cancelPopup = ref(null)

const refresherTriggered = ref(false)

/* ======================================= 数据初始化 ======================================= */
onLoad(async options => {
  try {
    showLoading("获取详情中...", true)
    formData.value.reportUnitId = userStore.unit
    formData.value.reporterId = userStore.userId
    formData.value.reporterPhone = userStore.phone
    await getRepairTypes()
    if (options.workOrderId) {
      workOrderId.value = options.workOrderId
      isNew.value = false
      await getWorkOrderDetail()
    } else {
      isNew.value = true
      isEditable.value = true
    }

    // 监听工单详情刷新事件
    uni.$on("workOrderDetailRefresh", handleWorkOrderRefresh)

    // 监听选择管理员事件
    uni.$on("selectManager", handleSelectManager)

    // 监听签名确认事件
    uni.$on("signatureConfirm", handleSignatureConfirm)
  } catch (error) {
    throw error
  } finally {
    hideLoading()
  }
  // 隐藏原生导航栏
  uni.hideNavigationBarLoading()
})

onUnload(() => {
  // 取消事件监听
  uni.$off("workOrderDetailRefresh", handleWorkOrderRefresh)
  uni.$off("signatureConfirm", handleSignatureConfirm)
  uni.$off("selectManager", handleSelectManager)
})

function handleWorkOrderRefresh() {
  if (workOrderId.value) {
    getWorkOrderDetail()
  }
}

// 获取报修详情
const getWorkOrderDetail = async () => {
  try {
    showLoading("获取详情中...", true)
    const res = await getWorkOrderDetailApi(workOrderId.value)
    const data = res.data.data
    for (let key in formData.value) {
      if (key === "attachments") {
        showAttachment.value = cloneDeep(data[key])
      }
      formData.value[key] = data[key]
    }
    showAttachment.value.forEach(item => {
      item.status = "success"
    })
    workOrderDetail.value = data
    repairTypeIndex.value = repairTypes.value.findIndex(item => item.id === formData.value.serviceClass)
    // 根据状态判断是否可编辑
    isEditable.value = workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_DISPATCH
  } catch (error) {
    showToast(error.message)
  } finally {
    hideLoading()
  }
}

const showStatus = computed(() => {
  return getWorkOrderStatus(workOrderDetail.value?.status)
})

function showContentMark(item) {
  return (
    item.location || item.remark || item.repairResult || item.evaluation || item.repairScore || item.signatureBase64
  )
}

/* ======================================= 报修类型 ======================================= */
// 选择报修类型
const repairTypeIndex = ref()
const onRepairTypeChange = e => {
  // 获取选中的索引值
  const selectedIndex = e.detail.value
  // 获取选中的 id 值
  formData.value.serviceClass = repairTypes.value[selectedIndex].id
  repairTypeIndex.value = selectedIndex
}

const repairTypeLabel = computed(() => {
  return repairTypes.value.find(item => item.id === formData.value.serviceClass)?.serviceClass || "--"
})

const repairTypes = ref([])
const repairTypesLoading = ref(false)
const getRepairTypes = async () => {
  repairTypesLoading.value = true
  getServiceContentApi()
    .then(res => {
      repairTypes.value = res.data.data || []
    })
    .catch(err => {
      showToast(err)
    })
    .finally(() => {
      repairTypesLoading.value = false
    })
}

/* ======================================= 附件 ======================================= */
const showAttachment = ref([])
function isImage(fileType) {
  return fileType.startsWith("image/")
}

function isVideo(fileType) {
  return fileType.startsWith("video/")
}

function chooseAttachment() {
  if (showAttachment.value.length >= 9) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.showActionSheet({
    itemList: ["上传图片", "上传视频"],
    title: "",
    success: res => {
      if (res.tapIndex === 0) {
        chooseImage()
      } else if (res.tapIndex === 1) {
        chooseVideo()
      }
    }
  })
}
// 选择图片或视频
const chooseImage = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseImage({
    count: remainCount,
    success: res => {
      const tempFiles = Array.isArray(res.tempFiles) ? res.tempFiles : [res.tempFiles]
      tempFiles.forEach(file => {
        // #ifdef APP
        file.type = "image/png"
        file.name = `附件${new Date().getTime()}.png}`
        // #endif
        if (file.size > 10 * 1024 * 1024) {
          showToast("文件大小不能超过10M")
          return
        }
        let data = {
          tempPath: file.path,
          fileType: file.type,
          fileName: file.name,
          fileId: "",
          status: "uploading",
          message: "上传中..."
        }

        showAttachment.value.push(data)
        uploadAttachment(file)
      })
    },
    fail: () => {
      showToast("选择图片失败")
    }
  })
}

const chooseVideo = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseVideo({
    count: 1,
    success: res => {
      let file = res.tempFile
      // #ifndef APP
      file.path = res.tempFilePath
      // #endif
      // #ifdef APP
      file = {
        path: res.tempFilePath,
        type: "video/mp4",
        name: `附件${new Date().getTime()}.mp4`,
        size: res.size
      }
      file.path = res.tempFilePath
      // #endif
      if (file.size > 10 * 1024 * 1024) {
        showToast("文件大小不能超过10M")
        return
      }
      let data = {
        tempPath: file.path,
        fileType: file.type,
        fileName: file.name,
        fileId: "",
        status: "uploading",
        message: "上传中..."
      }

      showAttachment.value.push(data)

      uploadAttachment(file)
    },
    fail: err => {
      uni.showModal({
        content: JSON.stringify(err)
      })
      console.log(err)
      showToast("选择视频失败")
    }
  })
}
// 上传附件
const uploadAttachment = file => {
  uploadWorkOrderAttachmentApi({ filePath: file.path })
    .then(res => {
      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.fileId = res.fileId
          item.status = "success"
          item.message = ""
        }
      })
      formData.value.attachments.push({
        fileId: res.fileId,
        fileName: res.filename,
        fileType: file.type
      })
    })
    .catch(err => {
      console.log(err)
      showToast(err.message)
      showAttachment.value.forEach(item => {
        if (item.tempPath === tempFilePath) {
          item.status = "error"
          item.message = err.message
        }
      })
    })
}

// 删除图片
const deleteImage = async file => {
  try {
    if (file.fileId && file.tempPath) {
      await deleteFile(file.fileId)
    }
    showAttachment.value = showAttachment.value.filter(
      item => (!item.fileId && item.tempPath !== file.tempPath) || (item.fileId && item.fileId !== file.fileId)
    )
    formData.value.attachments = formData.value.attachments.filter(item => item.fileId && item.fileId !== file.fileId)
  } catch (error) {
    showToast(error.message)
  }
}

/* ======================================= 操作权限 ======================================= */

// 是否可以取消报修
const canCancel = computed(() => {
  return [
    WorkOrderStatusEnum.WAITING_DISPATCH,
    WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH,
    WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME
  ].includes(workOrderDetail.value?.status)
})

// 是否可以联系维保人员
const canContactRepairPerson = computed(() => {
  return [
    WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME,
    WorkOrderStatusEnum.WAITING_REPAIR_PLAN,
    WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT,
    WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY,
    WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM,
    WorkOrderStatusEnum.PROCESSING,
    WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM,
    WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM,
    WorkOrderStatusEnum.FINISHED
  ].includes(workOrderDetail.value?.status)
})

// 是否可以联系报修管理人员
const canContactManager = computed(() => {
  return (
    (workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM ||
      workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM) &&
    !userStore.isAuth
  )
})

// 是否可以确认维修方案
const canConfirmPlan = computed(() => {
  return (
    workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM &&
    (userStore.isAuth || !workOrderDetail.value?.reportManagerId)
  )
})

// 是否可以确认维修完成
const canConfirmFinished = computed(() => {
  return (
    workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM &&
    (userStore.isAuth || !workOrderDetail.value?.finishManagerId)
  )
})

//是否显示按钮
const showFooterButtons = computed(() => {
  return !(
    workOrderDetail.value?.status === WorkOrderStatusEnum.FINISHED ||
    workOrderDetail.value?.status === WorkOrderStatusEnum.CANCELLED ||
    workOrderDetail.value?.status === WorkOrderStatusEnum.NO_NEED_REPAIR
  )
})

/* ======================================= 表单操作 ======================================= */
// 提交表单
const submitForm = async e => {
  const uploadingFile = showAttachment.value.filter(file => file.status === "uploading")
  if (uploadingFile.length > 0) return showToast("文件上传中，请等待文件上传后提交")
  // 表单验证
  if (!formData.value.detailLocation) {
    return uni.showToast({
      title: "请输入详细地址",
      icon: "none"
    })
  }
  if (!formData.value.serviceClass) {
    return uni.showToast({
      title: "请选择报修类型",
      icon: "none"
    })
  }
  if (!formData.value.faultDesc) {
    return uni.showToast({
      title: "请输入故障描述",
      icon: "none"
    })
  }

  try {
    let res
    showLoading("提交中...", true)
    if (isNew.value) {
      res = await createWorkOrderApi({ ...formData.value, source: "REPAIR_APP" })
    } else {
      res = await editWorkOrderApi({
        workOrderId: workOrderId.value,
        ...formData.value
      })
    }
    uni.$emit("repairOrderListRefresh")
    if (isNew.value) {
      uni.navigateBack()
    } else {
      getWorkOrderDetail()
    }
    uni.showToast({
      title: isNew.value ? "提交成功" : "更新成功",
      icon: "success"
    })
  } catch (error) {
    showToast(error.message)
  } finally {
    setTimeout(() => {
      hideLoading()
    }, 1500)
  }
}

// 确认取消
const confirmCancel = () => {
  cancelPopup.value.open()
}

// 处理取消
const handleCancel = async () => {
  showLoading("取消中...", true)
  cancelWorkOrderApi({
    workOrderId: workOrderId.value,
    reportUnitId: workOrderDetail.value.reportEnterprise?.id
  })
    .then(res => {
      uni.$emit("repairOrderListRefresh")
      getWorkOrderDetail()
      showToast("取消成功")
      cancelPopup.value.close()
    })
    .catch(err => {
      showToast(err.message || "取消失败")
    })
    .finally(() => {
      hideLoading()
    })
}

// 联系维保人员
const contactRepairPerson = () => {
  if (!workOrderDetail.value.maintenanceUser?.phone) {
    return showToast("维保人员电话为空")
  }
  uni.showActionSheet({
    itemList: ["拨打电话", "复制号码"],
    title: workOrderDetail.value.maintenanceUser?.phone,
    success: res => {
      if (res.tapIndex === 0) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: workOrderDetail.value?.maintenanceUser?.phone,
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none"
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 复制号码
        uni.setClipboardData({
          data: workOrderDetail.value?.maintenanceUser?.phone || "",
          success: () => {
            uni.showToast({
              title: "电话号码已复制",
              icon: "none"
            })
          }
        })
      }
    }
  })
}

// 联系报修管理人员
const contactManager = () => {
  if (
    (!workOrderDetail.value.reportManagerPhone &&
      workOrderDetail.value.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM) ||
    (!workOrderDetail.value.finishManagerPhone &&
      workOrderDetail.value.status === WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM)
  ) {
    return showToast("报修管理人员电话为空")
  }

  const phone =
    workOrderDetail.value.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM
      ? workOrderDetail.value.reportManagerPhone
      : workOrderDetail.value.finishManagerPhone

  uni.showActionSheet({
    itemList: ["拨打电话", "复制号码"],
    title: phone,
    success: res => {
      if (res.tapIndex === 0) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: phone,
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none"
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 复制号码
        uni.setClipboardData({
          data: phone || "",
          success: () => {
            uni.showToast({
              title: "电话号码已复制",
              icon: "none"
            })
          }
        })
      }
    }
  })
}

// 确认维修方案
const confirmPlan = () => {
  if (userStore.isAuth) {
    // 管理员直接签名确认
    uni.navigateTo({
      url: "/pages/signature-draw/signature-draw?pageType=workOrderConfirm"
    })
  } else {
    // 非管理员前往选择管理员页面
    uni.navigateTo({
      url: `/pages/repair-order/select-manager/index?workOrderId=${workOrderId.value}&mode=plan`
    })
  }
}

// 使用签名确认方案
const confirmPlanWithSignature = signatureBase64 => {
  console.log("使用签名确认方案")
  showLoading("确认中...", true)
  repairConfirmApi({
    workOrderId: workOrderId.value,
    reportUnitId: workOrderDetail.value.reportEnterprise?.id,
    signatureBase64
  })
    .then(res => {
      getWorkOrderDetail()
      showToast("确认成功")
      uni.$emit("repairOrderListRefresh")
    })
    .catch(err => {
      showToast(err.message)
    })
    .finally(() => {
      hideLoading()
    })
}

// 确认已完成并评价
const confirmFinished = () => {
  if (userStore.isAuth) {
    // 管理员直接前往评价页面
    uni.navigateTo({
      url: `/pages/repair-order/finish-rate/index?workOrderId=${workOrderId.value}&reportUnitId=${workOrderDetail.value.reportEnterprise?.id}`
    })
  } else {
    // 非管理员前往选择管理员页面
    uni.navigateTo({
      url: `/pages/repair-order/select-manager/index?workOrderId=${workOrderId.value}`
    })
  }
}

// 下拉刷新
const onRefresh = async () => {
  refresherTriggered.value = true
  try {
    if (!isNew.value) {
      await getWorkOrderDetail()
      uni.showToast({
        title: "刷新成功",
        icon: "success",
        duration: 1000
      })
    }
  } catch (error) {
    showToast(error.message || "刷新失败")
  } finally {
    refresherTriggered.value = false
  }
}

const handleSelectManager = async data => {
  if (workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM) {
    confirmPlanSelectManager(data)
  } else if (workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM) {
    confirmFinishedSelectManager(data)
  }
}

// 确认方案选择管理员
const confirmPlanSelectManager = async data => {
  showLoading("提交中...", true)
  selectRepairManagerConfirmApi({
    workOrderId: workOrderId.value,
    reportUnitId: userStore.unit,
    reportManagerId: data.selectedManager.userId,
    reportManagerPhone: data.selectedManager.phone
  })
    .then(res => {
      showToast("提交成功")
      getWorkOrderDetail()
      uni.$emit("repairOrderListRefresh")
    })
    .catch(err => {
      showToast(err.message)
    })
}

// 确认完成选择管理员
const confirmFinishedSelectManager = async data => {
  showLoading("提交中...", true)
  selectFinishManagerConfirmApi({
    workOrderId: workOrderId.value,
    reportUnitId: userStore.unit,
    finishManagerId: data.selectedManager.userId,
    finishManagerPhone: data.selectedManager.phone
  })
    .then(res => {
      showToast("提交成功")
      getWorkOrderDetail()
      uni.$emit("repairOrderListRefresh")
    })
    .catch(err => {
      showToast(err.message)
    })
}
// 处理签名确认
const handleSignatureConfirm = data => {
  if (data.signatureBase64 && workOrderDetail.value?.status === WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM) {
    confirmPlanWithSignature(data.signatureBase64)
  }
}
</script>

<style lang="scss" scoped>
.repair-detail {
  background-color: #f5f5f5;
  height: calc(100% - var(--status-bar-height) - 44px);

  .section {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin: 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      color: #333;
    }

    .section-hint {
      font-size: 26rpx;
      color: #aaa;
      margin: 5rpx 0;
    }

    .info-item {
      flex-direction: row !important;
      .label {
        width: 160rpx;
      }
    }

    .form-item,
    .info-item {
      margin-bottom: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 10rpx;

      .label {
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
        line-height: 1.2;
        .required {
          color: #f56c6c;
          margin-right: 10rpx;
        }
      }

      picker {
        width: 100%;
      }

      .picker-container {
        width: 100%;
        position: relative;
      }

      input,
      textarea,
      .picker-value {
        width: 100%;
        background-color: #f8f8f8;
        padding: 16rpx;
        box-sizing: border-box;
        border-radius: 8rpx;
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.loading {
          color: #999;

          .loading-icon {
            animation: loading-rotate 1s linear infinite;
            margin-right: 8rpx;
          }
        }

        &.empty {
          color: #999;
        }
      }

      @keyframes loading-rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      input {
        height: 68rpx;
      }

      textarea {
        height: 160rpx;
      }

      .value {
        flex: 1;
        min-width: 0rpx;
        font-size: 28rpx;
        line-height: 1.2;
        color: #333;
        box-sizing: border-box;
        white-space: pre-wrap;
        word-break: break-all;
      }

      .tag {
        flex: 0 0 auto;
        width: fit-content;
        border-radius: 12rpx;
        padding: 4rpx 12rpx;
        box-sizing: border-box;
        &-warning {
          color: #e6a23c;
          border: 1rpx solid #e6a23c;
          background-color: rgb(252.5, 245.7, 235.5);
        }

        &-success {
          color: #67c23a;
          border: 1rpx solid #67c23a;
          background-color: rgb(240, 249, 235);
          width: fit-content;
        }

        &-info {
          color: #909399;
          border: 1rpx solid #909399;
          background-color: rgb(245, 247, 250);
          width: fit-content;
        }

        &-danger {
          color: #f56c6c;
          border: 1rpx solid #f56c6c;
          background-color: rgb(253, 226, 226);
          width: fit-content;
        }
      }
    }
  }

  .upload-list {
    display: flex;
    flex-wrap: wrap;

    .upload-item {
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      border: 2rpx dashed #ddd;
    }
    .image-item {
      width: 200rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      border-radius: 8rpx;
      position: relative;
      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        font-size: 28rpx;
        color: #999;
        text-align: center;
        margin-top: 10rpx;
      }
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #fff;
        z-index: 1;
      }
    }

    .upload-item {
      border: 2rpx dashed #ddd;
      display: flex;
      justify-content: center;
      align-items: center;

      .upload-icon {
        font-size: 60rpx;
        color: #999;
      }
    }

    .image-item {
      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .delete-icon {
        position: absolute;
        top: -15rpx;
        right: -15rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        z-index: 2;
      }
    }
  }

  .timeline {
    .timeline-item {
      position: relative;
      padding-left: 30rpx;
      margin-bottom: 30rpx;
      box-sizing: border-box;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 10rpx;
        width: 16rpx;
        height: 16rpx;
        background-color: #3c9cff;
        border-radius: 50%;
      }

      &::after {
        content: "";
        position: absolute;
        left: 8rpx;
        top: 26rpx;
        width: 2rpx;
        height: calc(100% + 14rpx);
        background-color: #ddd;
      }

      &:last-child::after {
        display: none;
      }

      .time {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 6rpx;
      }

      .content {
        font-size: 28rpx;
        color: #333;
      }

      .content-mark {
        margin-top: 10rpx;
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        background-color: #f5f5f5;
        padding: 20rpx;
        box-sizing: border-box;
        border-radius: 10rpx;
        .signature-base64 {
          display: flex;
          align-items: center;
          .signature-base64-title {
            margin-right: 10rpx;
            white-space: nowrap;
          }
          image {
            height: 120rpx;
            background-color: #fff;
            padding: 10rpx;
            border-radius: 10rpx;
          }
        }
        .attachment-container {
          display: flex;
          .attachment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20rpx;
            .attachment-item {
              width: 120rpx;
              .file-name {
                font-size: 24rpx;
                color: #666;
                margin-top: 8rpx;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }

  .subsection {
    margin-top: 20rpx;
    margin-bottom: 20rpx;

    .subsection-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: bold;
    }
  }

  .service-items {
    .service-item {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .service-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-id {
          font-size: 26rpx;
          color: #666;
          font-weight: bold;
        }
      }

      .service-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .service-name {
          font-size: 28rpx;
          color: #333;
        }
      }

      .service-item-footer {
        display: flex;
        flex-direction: column;
        font-size: 26rpx;
        color: #666;
        .footer-row {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .footer-column {
            min-width: 50%;
            &:last-child {
              text-align: right;
            }
          }
        }
        .service-price {
          color: #f56c6c;
          white-space: nowrap;
        }

        .service-subtotal {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }

  .fee-statistics {
    margin-top: 30rpx;

    .fee-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
      border-bottom: 1px solid #eee;
      padding-bottom: 10rpx;
    }

    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12rpx 0;
      border-bottom: 1rpx solid #eee;

      .fee-label {
        font-size: 28rpx;
        color: #666;
      }

      .fee-value {
        font-size: 28rpx;
        color: #333;
      }
    }

    .fee-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      margin-top: 10rpx;

      .fee-label {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
      }

      .fee-value {
        font-size: 30rpx;
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }

  .price-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    border-top: 1px solid #eee;
    font-size: 28rpx;

    .label {
      color: #666;
    }

    .price-total {
      color: #ff5a5f;
      font-weight: bold;
    }
  }
}

.footer-buttons {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;

  .btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;
    margin: 0 10rpx;

    &-primary {
      background-color: #3c9cff;
      color: #fff;
    }

    &-default {
      background-color: #fff;
      color: #666;
      border: 1rpx solid #ddd;
    }
  }
}
</style>
