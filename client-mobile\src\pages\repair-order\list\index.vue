<template>
  <view class="page-container">
    <NavBar title="报修记录" />
    <view class="page-content-with-nav repair-list">
      <!-- 添加顶部标签导航 -->
      <view class="tab-container">
        <view class="tab-item" :class="{ active: activeTab === 'all' }" @click="switchTab('all')">全部</view>
        <view class="tab-item" :class="{ active: activeTab === 'progress' }" @click="switchTab('progress')"
          >进行中</view
        >
        <view class="tab-item" :class="{ active: activeTab === 'finished' }" @click="switchTab('finished')"
          >已完成</view
        >
      </view>

      <!-- 列表为空提示 -->
      <view class="list-empty" v-if="repairList.length === 0">
        <image src="/static/empty.png" mode="aspectFit"></image>

        <text>暂无报修记录</text>
      </view>

      <!-- 列表渲染 -->
      <scroll-view
        class="repair-scroll"
        scroll-y
        @scrolltolower="onReachBottom"
        @refresherrefresh="onPullDownRefresh"
        refresher-enabled
        :refresher-triggered="refreshing"
        show-scrollbar="false">
        <view
          class="repair-item"
          v-for="(item, index) in repairList"
          :key="index"
          @click="goToRepairDetail(item.workOrderId)">
          <view class="repair-header">
            <view class="repair-type">{{ item.workOrderId }}</view>
            <view class="repair-status" :style="{ color: getStatusColor(item.status) }">{{
              getStatusText(item.status)
            }}</view>
          </view>
          <view class="repair-content">
            <view class="repair-address">{{ `报修类型：${item.serviceClassLabel}` }}</view>
            <view class="repair-desc">{{ `故障描述：${item.faultDesc}` }}</view>
          </view>
          <view class="repair-footer">
            <view class="repair-time">{{ formatTime(item.reportTime) }}</view>
          </view>
        </view>

        <!-- 加载状态提示 -->
        <view class="loading-more" v-if="repairList.length > 0">
          <text v-if="hasMore && !refreshing">上拉加载更多</text>
          <text v-if="!hasMore">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { formatTime } from "@/utils"
import NavBar from "@/components/NavBar.vue"
import { getWorkOrderListApi } from "@/api"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const repairList = ref([])
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const refreshing = ref(false)
const activeTab = ref("all")

onLoad(() => {
  uni.$on("repairOrderListRefresh", resetRefresh)
  getRepairList()
})

onUnload(() => {
  uni.$off("repairOrderListRefresh", resetRefresh)
})

// 切换标签
const switchTab = (tab: string) => {
  if (activeTab.value === tab) return
  activeTab.value = tab
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  getRepairList()
}

// 重置刷新
function resetRefresh() {
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })
  getRepairList()
}

// 下拉刷新
const onPullDownRefresh = () => {
  refreshing.value = true
  pageNum.value = 1
  hasMore.value = true
  repairList.value = []
  getRepairList()
    .then(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
    .catch(() => {
      refreshing.value = false
      uni.stopPullDownRefresh()
    })
}

// 上拉加载更多
const onReachBottom = () => {
  if (hasMore.value && !refreshing.value) {
    pageNum.value++
    getRepairList()
  }
}

// 获取报修列表
const getRepairList = async () => {
  try {
    let filters = `reportUnitId=${userStore.unit},reporterId=${userStore.userId}`
    let commonStatus = ""
    if (activeTab.value === "progress") {
      commonStatus =
        "WAITING_DISPATCH,WAITING_COMPANY_DISPATCH,WAITING_REPAIR_PERSON_COME,WAITING_REPAIR_PLAN,WAITING_PLATFORM_AUDIT,WAITING_REPAIR_PLAN_MODIFY,WAITING_REPAIR_CONFIRM,PROCESSING,WAITING_REPAIR_FINISH_CONFIRM,WAITING_PLATFORM_FINISH_CONFIRM"
    } else if (activeTab.value === "finished") {
      commonStatus = "FINISHED,CANCELLED,NO_NEED_REPAIR"
    }

    const res = await getWorkOrderListApi({
      offset: (pageNum.value - 1) * pageSize.value,
      limit: pageSize.value,
      filters,
      commonStatus: commonStatus
    })
    const data = res.data.data
    if (pageNum.value === 1) {
      repairList.value = data.rows
    } else {
      repairList.value = [...repairList.value, ...data.rows]
    }
    hasMore.value = data.rows.length === pageSize.value
  } catch (error) {
    uni.showToast({
      title: "获取报修列表失败",
      icon: "none"
    })
  }
}

// 跳转到报修详情页
const goToRepairDetail = (workOrderId?: string) => {
  let url = "/pages/repair-order/detail/index"
  if (workOrderId) {
    url += `?workOrderId=${workOrderId}`
  }
  uni.navigateTo({
    url
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    WAITING_DISPATCH: "待派单",
    WAITING_COMPANY_DISPATCH: "待企业派单",
    WAITING_REPAIR_PERSON_COME: "待维修人员上门",
    WAITING_REPAIR_PLAN: "待维修方案",
    WAITING_PLATFORM_AUDIT: "待平台审核",
    WAITING_REPAIR_PLAN_MODIFY: "待修改维修方案",
    WAITING_REPAIR_CONFIRM: "待确认维修方案",
    PROCESSING: "维修中",
    WAITING_REPAIR_FINISH_CONFIRM: "待确认完成",
    WAITING_PLATFORM_FINISH_CONFIRM: "待平台确认完成",
    FINISHED: "已完成",
    CANCELLED: "已取消",
    NO_NEED_REPAIR: "无需维修"
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap = {
    WAITING_DISPATCH: "#ff9900",
    WAITING_COMPANY_DISPATCH: "#ff9900",
    WAITING_REPAIR_PERSON_COME: "#ff9900",
    WAITING_REPAIR_PLAN: "#ff9900",
    WAITING_PLATFORM_AUDIT: "#ff9900",
    WAITING_REPAIR_PLAN_MODIFY: "#ff9900",
    WAITING_REPAIR_CONFIRM: "#ff9900",
    PROCESSING: "#3c9cff",
    WAITING_REPAIR_FINISH_CONFIRM: "#3c9cff",
    WAITING_PLATFORM_FINISH_CONFIRM: "#3c9cff",
    FINISHED: "#19be6b",
    CANCELLED: "#999999",
    NO_NEED_REPAIR: "#999999"
  }
  return colorMap[status] || "#333333"
}
</script>

<style lang="scss">
.repair-list {
  padding: 0;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  height: 100%;

  .tab-container {
    display: flex;
    background: #fff;
    padding: 0 20rpx;
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    z-index: 10;

    .tab-item {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      padding: 20rpx 0;
      position: relative;
      box-sizing: border-box;

      &.active {
        color: #3c9cff;
        font-weight: bold;

        &:after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: #3c9cff;
          border-radius: 2rpx;
        }
      }
    }
  }

  .repair-scroll {
    flex: 1;
    min-height: 0px;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .list-empty {
    padding: 60rpx 0;
    box-sizing: border-box;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    image {
      width: 200rpx;
      height: 200rpx;
      object-fit: contain;
    }
  }

  .repair-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .repair-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .repair-type {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
      }

      .repair-status {
        font-size: 28rpx;
      }
    }

    .repair-content {
      margin-bottom: 16rpx;

      .repair-address {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }

      .repair-desc {
        font-size: 26rpx;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .repair-footer {
      display: flex;
      justify-content: space-between;
      font-size: 24rpx;
      color: #999;
    }
  }

  .loading-more {
    text-align: center;
    padding: 20rpx 0;
    box-sizing: border-box;
    color: #999;
    font-size: 24rpx;
  }
}
</style>
