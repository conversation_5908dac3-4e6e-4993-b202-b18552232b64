<template>
  <view class="reset-password-container">
    <NavBar title="忘记密码" />
    <view class="page-content-with-nav">
      <view class="form-box">
        <view class="step-box">
          <view class="step-item" :class="{ active: currentStep >= 1 }">
            <view class="step-number">1</view>
            <view class="step-desc">验证手机号</view>
          </view>
          <view class="step-line"></view>
          <view class="step-item" :class="{ active: currentStep >= 2 }">
            <view class="step-number">2</view>
            <view class="step-desc">重置密码</view>
          </view>
          <view class="step-line"></view>
          <view class="step-item" :class="{ active: currentStep >= 3 }">
            <view class="step-number">3</view>
            <view class="step-desc">完成</view>
          </view>
        </view>

        <!-- 第一步：验证手机号 -->
        <view class="step-content" v-if="currentStep === 1">
          <view class="form-item">
            <view class="label">手机号</view>
            <view class="input-box">
              <input class="input" type="text" v-model="formData.phone" placeholder="请输入手机号" />
            </view>
          </view>
          <view class="form-item">
            <view class="label">验证码</view>
            <view class="input-box code-box">
              <input class="input" type="text" v-model="formData.code" placeholder="请输入验证码" />
              <view class="code-btn" @click="getCode">{{ codeTips }}</view>
            </view>
          </view>
          <button class="primary-btn" @click="verifyPhone">下一步</button>
        </view>

        <!-- 第二步：重置密码 -->
        <view class="step-content" v-if="currentStep === 2">
          <view class="form-item">
            <view class="label">新密码</view>
            <view class="input-box">
              <input
                class="input"
                type="password"
                v-model="formData.password"
                placeholder="请输入6-20位新密码"
                maxlength="20" />
            </view>
          </view>
          <view class="form-item">
            <view class="label">确认密码</view>
            <view class="input-box">
              <input
                class="input"
                type="password"
                v-model="formData.confirmPassword"
                placeholder="请再次输入新密码"
                maxlength="20" />
            </view>
          </view>
          <button class="primary-btn" @click="resetPassword">确认修改</button>
        </view>

        <!-- 第三步：完成 -->
        <view class="step-content" v-if="currentStep === 3">
          <view class="success-icon">
            <uni-icons type="checkmarkempty" size="60" color="#4cd964"></uni-icons>
          </view>
          <view class="success-text">密码重置成功</view>
          <button class="primary-btn" @click="goToLogin">返回登录</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { NavBar } from "@/components"
import { showToast } from "@/utils"

// 当前步骤
const currentStep = ref(1)

// 表单数据
const formData = reactive({
  phone: "",
  code: "",
  password: "",
  confirmPassword: ""
})

// 验证码相关
const codeTips = ref("获取验证码")
const codeTime = ref(60)
let timer: any = null

// 获取验证码
const getCode = () => {
  if (codeTips.value !== "获取验证码") return

  if (!formData.phone) {
    uni.showToast({
      title: "请输入手机号",
      icon: "none"
    })
    return
  }

  codeTips.value = `${codeTime.value}s`
  timer = setInterval(() => {
    codeTime.value--
    codeTips.value = `${codeTime.value}s`
    if (codeTime.value <= 0) {
      clearInterval(timer)
      codeTips.value = "获取验证码"
      codeTime.value = 60
    }
  }, 1000)

  // 模拟验证码获取
  uni.showToast({
    title: "验证码已发送",
    icon: "none"
  })
}

// 验证手机号
const verifyPhone = () => {
  if (!formData.phone) {
    showToast("请输入手机号")
    return
  }
  if (!formData.code) {
    showToast("请输入验证码")
    return
  }

  // 模拟验证成功
  setTimeout(() => {
    currentStep.value = 2
  }, 500)
}

// 重置密码
const resetPassword = () => {
  if (!formData.password) {
    showToast("请输入新密码")
    return
  }
  if (formData.password.length < 6) {
    showToast("密码不能少于6位")
    return
  }
  if (formData.password !== formData.confirmPassword) {
    showToast("两次输入的密码不一致")
    return
  }

  // 模拟重置成功
  setTimeout(() => {
    currentStep.value = 3
  }, 1000)
}

// 返回登录
const goToLogin = () => {
  uni.navigateBack()
}
</script>

<style lang="scss">
.reset-password-container {
  background-color: #fff;
  min-height: 100vh;

  .form-box {
    padding: 40rpx;
    margin-top: 30rpx;
  }

  .step-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 60rpx;

    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .step-number {
        width: 60rpx;
        height: 60rpx;
        border-radius: 30rpx;
        background-color: #eee;
        color: #999;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        margin-bottom: 10rpx;
      }

      .step-desc {
        font-size: 24rpx;
        color: #999;
      }

      &.active {
        .step-number {
          background-color: $uni-color-primary;
          color: #fff;
        }
        .step-desc {
          color: $uni-color-primary;
        }
      }
    }

    .step-line {
      flex: 1;
      height: 2rpx;
      background-color: #eee;
      margin: 0 10rpx;
    }
  }

  .step-content {
    .form-item {
      margin-bottom: 40rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
      }

      .input-box {
        border-bottom: 1px solid #eee;
        padding: 20rpx 0;

        .input {
          font-size: 28rpx;
        }
      }

      .code-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .code-btn {
          color: $uni-color-primary;
          font-size: 26rpx;
          padding-left: 30rpx;
          border-left: 1px solid #eee;
        }
      }
    }

    .primary-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      background-color: $uni-color-primary;
      color: #fff;
      font-size: 30rpx;
      border-radius: 45rpx;
      margin-top: 60rpx;
    }

    .success-icon {
      display: flex;
      justify-content: center;
      margin: 60rpx 0 30rpx;
    }

    .success-text {
      text-align: center;
      font-size: 32rpx;
      color: #333;
      margin-bottom: 60rpx;
    }
  }
}
</style>
