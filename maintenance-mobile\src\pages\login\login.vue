<template>
  <view class="login-container">
    <view class="logo-box">
      <image class="logo" src="/static/logo.svg" mode="aspectFit"></image>
    </view>
    <view class="title">
      即时修维修工具
      <text class="subtitle">维修服务平台——测试版本</text>
    </view>
    <view class="tab-box">
      <view class="tab-item" :class="{ active: loginType === 'password' }" @click="changeLoginType('password')">
        密码登录</view
      >
      <!-- <view class="tab-item" :class="{ active: loginType === 'code' }" @click="changeLoginType('code')"
        >验证码登录
      </view> -->
    </view>
    <view class="form-box">
      <view class="input-item">
        <view class="input-prefix">
          <uni-icons type="phone" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="text"
          v-model="formData.phone"
          placeholder="请输入手机号"
          placeholder-class="input-placeholder" />
      </view>
      <view class="input-item" v-if="loginType === 'password'">
        <view class="input-prefix">
          <uni-icons type="locked" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="password"
          v-model="formData.password"
          placeholder="请输入登录密码"
          placeholder-class="input-placeholder" />
      </view>
      <view class="input-item" v-else>
        <view class="input-prefix">
          <uni-icons type="compose" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="text"
          v-model="formData.code"
          placeholder="请输入验证码"
          placeholder-class="input-placeholder" />
        <view class="code-btn" @click="getCode">{{ codeTips }}</view>
      </view>
      <view class="forgot-password" v-if="loginType === 'password'">
        <text @click="forgotPassword">忘记密码</text>
      </view>
    </view>
    <view class="login-btn-box">
      <button class="login-btn primary-btn" @click="handleLogin">登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { login } from "@/api"
import { UserTypeEnum } from "@/configs"
import { useUserStore } from "@/store/user"
import { showToast } from "@/utils"

const userStore = useUserStore()

// 登录类型
const loginType = ref("password")

// 表单数据
const formData = reactive({
  phone: "",
  password: "",
  code: ""
})

// 验证码相关
const codeTips = ref("获取验证码")
const codeTime = ref(60)
let timer: any = null

// 切换登录类型
const changeLoginType = (type: string) => {
  loginType.value = type
}

// 获取验证码
const getCode = () => {
  if (codeTips.value !== "获取验证码") return

  if (!formData.phone) {
    uni.showToast({
      title: "请输入手机号",
      icon: "none"
    })
    return
  }

  codeTips.value = `${codeTime.value}s`
  timer = setInterval(() => {
    codeTime.value--
    codeTips.value = `${codeTime.value}s`
    if (codeTime.value <= 0) {
      clearInterval(timer)
      codeTips.value = "获取验证码"
      codeTime.value = 60
    }
  }, 1000)

  // 模拟验证码获取
  uni.showToast({
    title: "验证码已发送",
    icon: "none"
  })
}

// 登录
const handleLogin = () => {
  // 表单验证
  if (!formData.phone) {
    uni.showToast({
      title: "请输入手机号",
      icon: "none"
    })
    return
  }

  if (loginType.value === "password" && !formData.password) {
    uni.showToast({
      title: "请输入密码",
      icon: "none"
    })
    return
  }

  if (loginType.value === "code" && !formData.code) {
    uni.showToast({
      title: "请输入验证码",
      icon: "none"
    })
    return
  }
  uni.showLoading({
    title: "登录中"
  })

  login({
    phone: formData.phone,
    password: formData.password,
    userType: UserTypeEnum.MAINTENANCE_UNIT,
    source: "MAINTENANCE_APP"
  })
    .then(res => {
      const data = res.data
      userStore.setUserInfo({ ...data.user, token: data.token })
      uni.hideLoading()
      // 跳转到首页

      uni.switchTab({
        url: "/pages/home/<USER>"
      })
    })
    .catch(err => {
      uni.hideLoading()
      showToast(err.message)
    })
}

// 忘记密码
const forgotPassword = () => {
  uni.navigateTo({
    url: "/pages/login/forget-password"
  })
}
</script>

<style lang="scss">
.login-container {
  padding: 150rpx 40rpx;
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;

  .logo-box {
    display: flex;
    justify-content: center;
    margin-top: 60rpx;

    .logo {
      width: 180rpx;
      height: 180rpx;
    }
  }

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-top: 20rpx;

    .subtitle {
      display: block;
      font-size: 24rpx;
      font-weight: normal;
      color: #999;
      margin-top: 10rpx;
    }
  }

  .tab-box {
    display: flex;
    justify-content: center;
    margin-top: 60rpx;

    .tab-item {
      position: relative;
      padding: 0 30rpx 20rpx;
      font-size: 28rpx;
      color: #999;
      margin: 0 20rpx;

      &.active {
        color: $uni-color-primary;
        font-weight: bold;

        &:after {
          content: "";
          position: absolute;
          bottom: -2rpx;
          left: 0;
          width: 100%;
          height: 4rpx;
          background-color: $uni-color-primary;
          border-radius: 2rpx;
        }
      }
    }
  }

  .form-box {
    margin-top: 60rpx;

    .input-item {
      display: flex;
      align-items: center;
      height: 100rpx;
      margin-bottom: 30rpx;
      border-bottom: 1px solid #eee;

      .input-prefix {
        width: 60rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .uni-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
      }

      .code-btn {
        padding: 0 20rpx;
        color: $uni-color-primary;
        font-size: 28rpx;
      }
    }

    .forgot-password {
      text-align: right;
      font-size: 26rpx;
      color: #999;
      margin-top: 20rpx;
    }
  }

  .login-btn-box {
    margin-top: 80rpx;

    .login-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      font-size: 32rpx;
      border-radius: 45rpx;
      background-color: $uni-color-primary;
      color: #fff;
      border: none;
    }
  }
}
</style>
