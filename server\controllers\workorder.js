const { WorkOrder } = require("../models/WorkOrder");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");
const { RepairEnterprise } = require("../models/RepairEnterprise");
const { MaintenanceEnterprise } = require("../models/MaintenanceEnterprise");
const { MaintenanceUser } = require("../models/MaintenanceUser");
const { PlatformUser } = require("../models/PlatformUser");
const { RepairUser } = require("../models/RepairUser");
const { getFilterObj } = require("../utils/filters");
const FileModel = require("../models/File");
const {
  getWorkOrderListUtil,
  getRepairPriceUtil,
} = require("../utils/workorder");
const { get } = require("http");
// 获取工单列表
exports.getWorkOrderList = async (req, res) => {
  try {
    const {
      filters,
      offset,
      limit,
      reporter,
      repair,
      commonStatus,
      showMask = "true",
    } = req.query;
    let filtersObj = {};

    if (filters) {
      filtersObj = getFilterObj(filters, WorkOrder.schema);
    }
    const { workOrderList, total } = await getWorkOrderListUtil({
      filtersObj,
      offset,
      limit,
      reporter,
      repair,
      commonStatus,
      showMask,
    });
    return res.status(200).json({
      code: 200,
      message: "获取工单列表成功",
      data: {
        rows: workOrderList,
        pageElements: {
          totalElements: total,
          pageSize: Number(limit),
        },
      },
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取工单列表失败",
      error: error.message || "未知错误",
    });
  }
};

// 报修管理员获取待办列表
exports.getWorkOrderListByRepairManager = async (req, res) => {
  try {
    const {
      filters,
      offset,
      limit,
      reporter,
      repair,
      commonStatus,
      showMask = "true",
    } = req.query;
    const filtersObj = getFilterObj(filters, WorkOrder.schema);
    filtersObj.$or = [
      { reporterId: req.user.userId },
      { reportManagerId: req.user.userId, status: "WAITING_REPAIR_CONFIRM" },
      {
        finishManagerId: req.user.userId,
        status: "WAITING_REPAIR_FINISH_CONFIRM",
      },
    ];
    const { workOrderList, total } = await getWorkOrderListUtil({
      filtersObj,
      offset,
      limit,
      reporter,
      repair,
      commonStatus,
      showMask,
    });

    return res.status(200).json({
      code: 200,
      message: "获取工单列表成功",
      data: {
        rows: workOrderList,
        pageElements: {
          totalElements: total,
        },
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      code: 500,
      message: "获取工单列表失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取工单详情
exports.getWorkOrderDetail = async (req, res) => {
  try {
    const workOrderId = req.query.workOrderId;

    const workOrder = (await WorkOrder.findOne({ workOrderId })).toObject();

    if (!workOrder) {
      return res.status(404).json({
        code: 404,
        message: "未找到该工单",
      });
    }

    const users = workOrder.repairProcess?.map((item) => item.processor) || [];
    users.push(workOrder.reporterId);
    users.push(workOrder.maintenanceUserId);

    const repairUsers = await RepairUser.find({
      userId: { $in: users },
    }).select("-_id userId username phone");
    const maintenanceUsers = await MaintenanceUser.find({
      userId: { $in: users },
    }).select("-_id userId username phone");
    const platformUsers = await PlatformUser.find({
      userId: { $in: users },
    }).select("-_id userId username phone");
    const userList = [...repairUsers, ...maintenanceUsers, ...platformUsers];

    workOrder.reportEnterprise = await RepairEnterprise.findOne({
      id: workOrder.reportUnitId,
    }).select("-_id id companyName contactPhone contactPerson");

    workOrder.maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: workOrder.maintenanceUnitId,
    }).select("-_id id companyName contactPhone contactPerson");

    workOrder.reporter = userList.find(
      (item) => item.userId === workOrder.reporterId
    );

    workOrder.maintenanceUser = userList.find(
      (item) => item.userId === workOrder.maintenanceUserId
    );

    workOrder.repairProcess = workOrder.repairProcess.map((item) => {
      const user = userList.find((user) => user.userId === item.processor);
      return {
        ...item,
        processorName: user?.username || "",
      };
    });

    return res.status(200).json({
      code: 200,
      message: "获取工单详情成功",
      data: workOrder,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取工单详情失败",
      error: error.message || "未知错误",
    });
  }
};

// 创建工单
exports.createWorkOrder = async (req, res) => {
  try {
    const data = req.body;
    if (!data.source) {
      return res.status(400).json({
        code: 400,
        message: "报修来源为必填项",
      });
    }

    // 生成工单号
    const workOrderId = WorkOrder.generateWorkOrderId();

    let processType = "";
    data.creatorId = req.user.userId;
    data.creatorUnitId = req.user.unit || "";
    if (data.source === "PLATFORM") {
      processType = "平台调度";
      data.creatorUnitId = "";
    } else if (data.source === "REPAIR_APP") {
      const reportEnterprise = await RepairEnterprise.findOne({
        id: req.user.unit,
      }).select("-_id id companyName");
      processType = reportEnterprise.companyName;
    } else if (data.source === "MAINTENANCE_APP") {
      const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
        id: req.user.unit,
      }).select("-_id id companyName");
      processType = maintenanceEnterprise.companyName;
    }

    const repairProcess = [
      {
        processTime: new Date().getTime(),
        processor:
          data.source === "MAINTENANCE_APP" ? data.creatorId : data.reporterId,
        processType: processType,
        processResult: data.reportWay === "PHONE" ? "电话报修" : "自助报修",
      },
    ];
    // 创建工单
    const workOrder = new WorkOrder({
      workOrderId,
      ...data,
      repairProcess,
      status: "WAITING_DISPATCH",
    });

    await workOrder.save();

    return res.status(201).json({
      code: 200,
      message: "工单创建成功",
      data: workOrder,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "创建工单失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑工单
exports.editWorkOrder = async (req, res) => {
  try {
    const data = req.body;
    const workOrder = await WorkOrder.findOne({
      workOrderId: data.workOrderId,
    });
    const oldAttachments = workOrder.attachments;
    const newAttachments = data.attachments.map((item) => item.fileId);
    const deleteAttachments = oldAttachments
      .filter((item) => !newAttachments.includes(item.fileId))
      .map((item) => item.fileId);

    // 删除删除的文件
    if (deleteAttachments.length > 0) {
      await FileModel.deleteFile(deleteAttachments);
    }

    // 更新工单
    await WorkOrder.findOneAndUpdate(
      { workOrderId: data.workOrderId },
      { ...data }
    );

    return res.status(200).json({
      code: 200,
      message: "工单更新成功",
      data: workOrder,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "更新工单失败",
      error: error.message || "未知错误",
    });
  }
};

// 上传工单附件
exports.uploadWorkOrderAttachment = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: "未检测到上传文件",
      });
    }

    const file = req.file;

    // 读取文件内容
    const fileContent = fs.readFileSync(file.path);

    // 准备存储到MongoDB的数据
    const fileData = {
      filename: file.originalname,
      mimeType: file.mimetype, // 确保和模型字段名一致
      size: file.size,
      data: fileContent, // 实际文件内容作为Buffer存储
    };

    // 添加元数据（如果存在）
    if (req.body && req.body.metadata) {
      try {
        // 尝试解析metadata，如果它是字符串形式的JSON
        const metadataStr = req.body.metadata;
        fileData.metadata =
          typeof metadataStr === "string"
            ? JSON.parse(metadataStr)
            : metadataStr;
      } catch (e) {
        fileData.metadata = req.body.metadata;
      }
    } else {
      fileData.metadata = {};
    }

    const savedFile = await FileModel.saveFile(fileData);

    // 删除临时文件
    fs.unlinkSync(file.path);

    return res.status(201).json({
      message: "附件上传成功",
      fileId: savedFile.fileId,
      filename: savedFile.filename,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "附件上传失败",
      error: error.message || "未知错误",
    });
  }
};

// 派单/转派
exports.dispatchWorkOrder = async (req, res) => {
  const {
    maintenanceUnitId,
    maintenanceUserId,
    isTransfer,
    isPlatform,
    workOrderId,
  } = req.body;
  try {
    if (!maintenanceUnitId) {
      return res.status(400).json({
        success: false,
        message: "接单单位为必填项",
      });
    }

    const maintenanceUnit = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
      status: "ENABLED",
    }).select("-_id id companyName");
    if (!maintenanceUnit) {
      return res.status(400).json({
        success: false,
        message: "接单单位不存在或已被禁用",
      });
    }

    let maintenanceUser = null;
    if (maintenanceUserId) {
      maintenanceUser = await MaintenanceUser.findOne({
        userId: maintenanceUserId,
        status: "ENABLED",
      }).select("-_id userId username");
      if (!maintenanceUser) {
        return res.status(400).json({
          success: false,
          message: "接单人不存在或已被禁用",
        });
      }
    }

    const workOrder = await WorkOrder.findOneAndUpdate(
      { workOrderId },
      {
        $set: {
          maintenanceUnitId,
          maintenanceUserId,
          status: maintenanceUserId
            ? "WAITING_REPAIR_PERSON_COME"
            : "WAITING_COMPANY_DISPATCH",
        },
        $push: {
          repairProcess: {
            processTime: new Date().getTime(),
            processor: req.user.userId,
            processType: `${
              isPlatform ? "平台" : maintenanceUnit.companyName
            }调度`,
            processResult: `已${isTransfer === "true" ? "转派" : "派单"}给${
              maintenanceUser
                ? maintenanceUser.username
                : maintenanceUnit.companyName
            }`,
          },
        },
      },
      { new: true }
    );

    if (!workOrder) {
      return res.status(404).json({
        success: false,
        message: "未找到该工单",
      });
    }

    res.status(200).json({
      success: true,
      message: `${isTransfer ? "转派" : "派单"}成功`,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: `${isTransfer ? "转派" : "派单"}失败`,
      error: error.message || "未知错误",
    });
  }
};

// 更新工单状态
exports.updateWorkOrderStatus = async (req, res) => {
  try {
    const workOrderId = req.params.id;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: "状态为必填项",
      });
    }

    const workOrder = await WorkOrder.findOne({ workOrderId });

    if (!workOrder) {
      return res.status(404).json({
        success: false,
        message: "未找到该工单",
      });
    }

    // 更新状态
    workOrder.status = status;

    // 如果状态为已完成，添加完成时间
    if (status === "已完成") {
      workOrder.completeTime = new Date();
    }

    await workOrder.save();

    res.status(200).json({
      success: true,
      data: workOrder,
      message: "工单状态更新成功",
    });
  } catch (error) {
    console.error("更新工单状态失败:", error);
    res.status(500).json({
      success: false,
      message: "服务器错误，无法更新工单状态",
    });
  }
};

// 获取工单数量
exports.getWorkOrderCount = async (req, res) => {
  try {
    const { maintenanceUnitId, maintenanceUserId, reportUnitId, reporterId } =
      req.query;
    const filtersObj = {};
    if (maintenanceUnitId) {
      filtersObj.maintenanceUnitId = maintenanceUnitId;
    }
    if (maintenanceUserId) {
      filtersObj.maintenanceUserId = maintenanceUserId;
    }
    if (reportUnitId) {
      filtersObj.reportUnitId = reportUnitId;
    }
    if (reporterId) {
      filtersObj.reporterId = reporterId;
    }
    // 获取不同status的数量
    const statusCount = await WorkOrder.aggregate([
      { $match: filtersObj },
      { $group: { _id: "$status", count: { $sum: 1 } } },
    ]);
    // 获取该公司的待派单数量
    const waitingCompanyDispatch = await WorkOrder.countDocuments({
      maintenanceUnitId,
      status: "WAITING_COMPANY_DISPATCH",
    });
    const data = [
      {
        _id: "WAITING_COMPANY_DISPATCH",
        count: waitingCompanyDispatch,
      },
      ...statusCount,
    ];
    return res.status(200).json({
      success: true,
      data,
      message: "获取工单数量成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "获取工单数量失败",
      error: error.message || "未知错误",
    });
  }
};

// 工单无需维修
exports.MaintenanceFree = async (req, res) => {
  try {
    const { workOrderId, maintenanceUnitId, reason } = req.body;

    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
        error: error.message || "未知错误",
      });

    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });

    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: maintenanceEnterprise.companyName,
      processResult: `无需维修`,
      reason,
    });
    workOrder.status = "NO_NEED_REPAIR";

    workOrder.save();
    return res.status(200).json({
      success: true,
      message: "修改成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "修改失败",
      error: error.message || "未知错误",
    });
  }
};

// 确认到达
exports.confirmArrival = async (req, res) => {
  try {
    const { workOrderId, maintenanceUnitId, location } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });

    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });

    workOrder.status = "WAITING_REPAIR_PLAN";
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: maintenanceEnterprise.companyName,
      processResult: `已确认到达客户现场`,
      location,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "确认到达成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "确认到达失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取报修方维修价格
exports.getRepairPrice = async (req, res) => {
  try {
    const data = await getRepairPriceUtil(req.body);
    return res.status(200).json({
      success: true,
      data: data,
      message: "获取报修方维修价格成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "获取报修方维修价格失败",
      error: error.message || "未知错误",
    });
  }
};

// 处理与报价
exports.processAndPrice = async (req, res) => {
  try {
    const {
      workOrderId,
      maintenanceUnitId,
      processDescription,
      distance,
      serviceItems,
      type,
      source,
    } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    if (type === "create") workOrder.status = "WAITING_PLATFORM_AUDIT";
    else if (type === "audit") workOrder.status = "WAITING_REPAIR_CONFIRM";
    else
      return res.status(400).json({
        success: false,
        message: "未知type",
      });

    if (processDescription) workOrder.processingOpinion = processDescription;
    workOrder.distance = distance;
    workOrder.serviceItems = serviceItems;
    const price = await getRepairPriceUtil({
      serviceItems,
      settlementType:
        serviceItems[0]?.reportUnitSettlementType || "IMMEDIATELY",
      distance,
      workOrderId,
    });
    workOrder.reportUnitSettlementType =
      serviceItems[0]?.reportUnitSettlementType || "IMMEDIATELY";
    workOrder.serviceFee = price.serviceFee;
    workOrder.transportFee = price.transportFee;
    workOrder.partsTotal = price.partsTotal;
    workOrder.totalPrice = price.totalPrice;
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType:
        source === "PLATFORM" ? "平台调度" : maintenanceEnterprise.companyName,
      processResult: type === "create" ? `已提交维修方案` : `已修改维修方案`,
    });

    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "处理与报价成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "处理与报价失败",
      error: error.message || "未知错误",
    });
  }
};

// 平台审核方案
exports.platformAudit = async (req, res) => {
  try {
    const {
      workOrderId,
      isPass,
      auditReason,
      serviceItems,
      partsTotal,
      distance,
    } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });

    workOrder.serviceItems = serviceItems;
    workOrder.distance = distance;
    // 审核的时候会填入配件价格，此时需要传入最新的配件价格
    const price = await getRepairPriceUtil({
      serviceItems,
      settlementType:
        serviceItems[0]?.reportUnitSettlementType || "IMMEDIATELY",
      distance,
      workOrderId,
      partsTotal,
    });
    workOrder.serviceFee = price.serviceFee;
    workOrder.transportFee = price.transportFee;
    workOrder.totalPrice = price.totalPrice;
    workOrder.partsTotal = partsTotal;

    if (isPass) {
      workOrder.status = "WAITING_REPAIR_CONFIRM";
    } else {
      workOrder.status = "WAITING_REPAIR_PLAN_MODIFY";
    }

    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: "平台调度",
      processResult: isPass ? "审核通过" : "审核不通过",
      reason: auditReason,
    });

    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "平台审核方案成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "平台审核方案失败",
      error: error.message || "未知错误",
    });
  }
};

// 报修方确认维修方案
exports.repairConfirm = async (req, res) => {
  try {
    const { workOrderId, reportUnitId, signatureBase64 } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    const reportEnterprise = await RepairEnterprise.findOne({
      id: reportUnitId,
    });
    workOrder.status = "PROCESSING";
    workOrder.repairTime = new Date().getTime();
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: reportEnterprise.companyName,
      processResult: "已确认维修方案",
      signatureBase64,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "报修方确认维修方案成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "报修方确认维修方案失败",
      error: error.message || "未知错误",
    });
  }
};

// 退单
exports.returnOrder = async (req, res) => {
  try {
    const { workOrderId, maintenanceUnitId, reason, remark } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });
    workOrder.status = "WAITING_DISPATCH";
    workOrder.maintenanceUnitId = "";
    workOrder.maintenanceUserId = "";
    workOrder.processingOpinion = "";
    workOrder.reportManagerId = "";
    workOrder.reportManagerPhone = "";
    workOrder.totalPrice = 0;

    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: maintenanceEnterprise.companyName,
      processResult: "退单",
      reason,
      remark,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "退单成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "退单失败",
      error: error.message || "未知错误",
    });
  }
};

// 完成维修
exports.completeRepair = async (req, res) => {
  try {
    const {
      workOrderId,
      maintenanceUnitId,
      signatureBase64,
      attachments,
      remark,
    } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });

    workOrder.status = "WAITING_REPAIR_FINISH_CONFIRM";
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });
    workOrder.completeTime = new Date().getTime();
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: maintenanceEnterprise.companyName,
      processResult: "已完成维修",
      signatureBase64,
      attachments,
      remark,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "完成维修成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "完成维修失败",
      error: error.message || "未知错误",
    });
  }
};

// 报修方确认完成
exports.repairFinishConfirm = async (req, res) => {
  try {
    const {
      workOrderId,
      reportUnitId,
      rateScore,
      rateComment,
      repairResult,
      signatureBase64,
    } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    const reportEnterprise = await RepairEnterprise.findOne({
      id: reportUnitId,
    });
    workOrder.status = "WAITING_PLATFORM_FINISH_CONFIRM";
    workOrder.evaluation = rateComment;
    workOrder.repairScore = rateScore;
    workOrder.repairResult = repairResult;
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: reportEnterprise.companyName,
      processResult: "已确认完成",
      evaluation: rateComment,
      repairScore: rateScore,
      repairResult: repairResult === "是" ? "设备已正常工作" : "设备未正常工作",
      signatureBase64,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "报修方确认完成成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "报修方确认完成失败",
      error: error.message || "未知错误",
    });
  }
};

// 平台确认完成
exports.platformFinishConfirm = async (req, res) => {
  try {
    const { workOrderId } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });

    workOrder.status = "FINISHED";
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: "平台调度",
      processResult: "已确认完成",
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "平台确认完成成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "平台确认完成失败",
      error: error.message || "未知错误",
    });
  }
};

//取消工单
exports.cancelWorkOrder = async (req, res) => {
  try {
    const { workOrderId, reportUnitId } = req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    const reportEnterprise = await RepairEnterprise.findOne({
      id: reportUnitId,
    });
    workOrder.status = "CANCELLED";
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: reportEnterprise.companyName,
      processResult: "取消工单",
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "取消工单成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "取消工单失败",
      error: error.message || "未知错误",
    });
  }
};

// 选择报修管理人确认方案
exports.selectRepairManagerConfirm = async (req, res) => {
  try {
    const { workOrderId, reportManagerId, reportManagerPhone, reportUnitId } =
      req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });

    const reportEnterprise = await RepairEnterprise.findOne({
      id: reportUnitId,
    });

    const manager = await RepairUser.findOne({
      userId: reportManagerId,
    });
    if (!manager)
      return res.status(404).json({
        success: false,
        message: "报修管理人不存在",
      });

    workOrder.reportManagerId = reportManagerId;
    workOrder.reportManagerPhone = reportManagerPhone;
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: reportEnterprise.companyName,
      processResult: `请求【${manager.username}】确认方案`,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "选择报修管理人确认方案成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "选择报修管理人确认方案失败",
      error: error.message || "未知错误",
    });
  }
};

// 选择确认完成报修管理人
exports.selectFinishManagerConfirm = async (req, res) => {
  try {
    const { workOrderId, reportUnitId, finishManagerId, finishManagerPhone } =
      req.body;
    const workOrder = await WorkOrder.findOne({ workOrderId });
    if (!workOrder)
      return res.status(404).json({
        success: false,
        message: "工单不存在",
      });
    const reportEnterprise = await RepairEnterprise.findOne({
      id: reportUnitId,
    });

    const manager = await RepairUser.findOne({
      userId: finishManagerId,
    });
    if (!manager)
      return res.status(404).json({
        success: false,
        message: "确认完成报修管理人不存在",
      });

    workOrder.finishManagerId = finishManagerId;
    workOrder.finishManagerPhone = finishManagerPhone;
    workOrder.repairProcess.push({
      processor: req.user.userId,
      processTime: new Date().getTime(),
      processType: reportEnterprise.companyName,
      processResult: `请求【${manager.username}】确认完成`,
    });
    await workOrder.save();
    return res.status(200).json({
      success: true,
      message: "选择确认完成报修管理人成功",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "选择确认完成报修管理人失败",
      error: error.message || "未知错误",
    });
  }
};
