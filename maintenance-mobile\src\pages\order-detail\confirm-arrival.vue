<template>
  <view class="page-container">
    <NavBar title="确认到达" />
    <view class="page-content-with-nav location-page">
      <view class="location-title">当前位置</view>
      <view class="map-container">
        <map
          id="locationMap"
          class="location-map"
          :latitude="latitude"
          :longitude="longitude"
          :markers="markers"
          :show-location="true"
          scale="16"></map>
      </view>
      <view class="location-address">
        <view class="address-title">位置地址</view>
        <view class="address-content">{{ locationName || "获取地址中..." }}</view>
      </view>
      <view class="action-button">
        <button class="submit-btn" @click="handleConfirmArrival">确认到达</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { NavBar } from "@/components"
import { showLoading, hideLoading, showToast } from "@/utils"
import { confirmArrival } from "@/api/workorder"
import { useUserStore } from "@/store/user"
import { onLoad } from "@dcloudio/uni-app"

const userStore = useUserStore()

// 定义参数
const workOrderId = ref("")
const latitude = ref(0)
const longitude = ref(0)
const locationName = ref("")

// 地图标记点
const markers = ref<any[]>([])

// 页面加载时处理
onLoad((options: any) => {
  if (options) {
    workOrderId.value = options.workOrderId || ""
    latitude.value = Number(options.latitude) || 0
    longitude.value = Number(options.longitude) || 0

    // 设置标记点
    markers.value = [
      {
        id: 1,
        latitude: latitude.value,
        longitude: longitude.value,
        iconPath: "/static/location-marker.png",
        width: 30,
        height: 30
      }
    ]

    // 获取地址信息
    getAddressFromLocation()
  }
})

// 根据经纬度获取地址信息
const getAddressFromLocation = () => {
  //   showLoading("获取地址信息中...")
  //   uni.request({
  //     url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude.value},${longitude.value}&key=YOUR_KEY`, // 这里需要使用真实的腾讯地图API KEY
  //     success: (res: any) => {
  //       hideLoading()
  //       if (res.data && res.data.result) {
  //         locationName.value = res.data.result.address || "未知地址"
  //       } else {
  //         locationName.value = "未能获取准确地址"
  //       }
  //     },
  //     fail: () => {
  //       hideLoading()
  //       locationName.value = "获取地址失败"
  //     }
  //   })
}

// 确认到达
const handleConfirmArrival = async () => {
  try {
    showLoading("提交确认到达信息...")

    const location = {
      longitude: longitude.value,
      latitude: latitude.value,
      address: locationName.value
    }

    await confirmArrival({
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      location: JSON.stringify(location)
    })

    hideLoading()
    showToast("确认到达成功")

    // 返回上一页并刷新
    setTimeout(() => {
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      // 调用上一页的方法刷新数据
      prevPage.$vm.getWorkOrderDetail && prevPage.$vm.getWorkOrderDetail()
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    hideLoading()
    showToast("确认到达失败")
    console.error(error)
  }
}
</script>

<style lang="scss">
.location-page {
  display: flex;
  flex-direction: column;
  padding: 20rpx;

  .location-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
  }

  .map-container {
    width: 100%;
    height: 500rpx;
    border-radius: 12rpx;
    overflow: hidden;
    margin-bottom: 30rpx;

    .location-map {
      width: 100%;
      height: 100%;
    }
  }

  .location-address {
    background-color: #f5f6fa;
    padding: 20rpx;
    border-radius: 12rpx;
    margin-bottom: 40rpx;

    .address-title {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
    }

    .address-content {
      font-size: 30rpx;
      color: #333;
      line-height: 1.5;
    }
  }

  .action-button {
    margin-top: auto;
    padding: 20rpx 0;

    .submit-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      background-color: #3c9cff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 45rpx;
      text-align: center;
    }
  }
}
</style>
