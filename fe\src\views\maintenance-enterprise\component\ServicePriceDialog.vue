<template>
  <CommonDialog
    v-model:visible="visible"
    title="配置服务项目结算价格"
    width="1000px"
    no-footer
  >
    <div class="service-price-dialog">
      <div class="service-price-layout">
        <!-- 左侧服务类别列表 -->
        <div class="service-category-panel">
          <div class="panel-header">
            <h3>服务类别</h3>
          </div>
          <div class="panel-content">
            <div
              v-for="(category, index) in serviceClasses"
              :key="index"
              :class="[
                'category-item',
                { active: activeCategory === category.id },
              ]"
              @click="handleCategoryClick(category)"
            >
              {{ category.serviceClass }}
            </div>
          </div>
        </div>

        <!-- 右侧服务项目列表 -->
        <div class="service-items-panel">
          <div class="panel-header">
            <h3>服务项目</h3>
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索服务项目"
                clearable
              >
                <template #append>
                  <el-button :icon="Search" @click="handleSearch"> </el-button>
                </template>
              </el-input>
            </div>
          </div>
          <div class="panel-content" v-loading="isLoadingItems">
            <div class="panel-tip">
              <el-alert
                :title="`提示：请为每个服务项目设置结算价格和税率，结算价不可超过最高限价。\n默认结算价格为服务内容配置的成本最高限价，默认税率为服务内容配置配置的税率。`"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>
            <el-table
              :data="serviceItems"
              style="width: 100%"
              border
              size="small"
              height="560"
            >
              <el-table-column
                prop="serviceClassName"
                label="服务类型"
                min-width="160"
              />
              <el-table-column prop="code" label="编号" min-width="120" />
              <el-table-column
                prop="serviceItem"
                label="服务项目"
                min-width="240"
              />
              <el-table-column prop="unit" label="单位" min-width="80" />
              <el-table-column prop="maxPrice" label="最高限价" min-width="100">
                <template #default="{ row }">
                  {{ row.maxPrice }}
                </template>
              </el-table-column>
              <el-table-column label="结算价" min-width="120" fixed="right">
                <template #default="{ row }">
                  <el-input
                    v-model="row.settlementPrice"
                    placeholder="请输入"
                    type="number"
                    @change="
                      (e) => {
                        if (row.settlementPrice > row.maxPrice) {
                          row.settlementPrice = row.maxPrice;
                          Message.error('结算价不能超过最高限价');
                        } else if (row.settlementPrice < 0) {
                          row.settlementPrice = 0;
                          Message.error('结算价不能小于0');
                        }
                      }
                    "
                  />
                </template>
              </el-table-column>
              <el-table-column label="税率" min-width="100" fixed="right">
                <template #default="{ row }">
                  <el-input
                    v-model="row.customTaxRate"
                    placeholder="请输入"
                    type="number"
                  >
                    <template #suffix>%</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="55" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    link
                    :loading="row.editLoading"
                    @click="(e) => handleSave(row)"
                  >
                    保存
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script setup>
import { ref, computed, nextTick } from "vue";
import { CommonDialog } from "@/base-components";
import {
  getServiceContentApi,
  getServiceItemApi,
  getMaintenanceServiceItemApi,
  editMaintenanceEnterpriseSettlementPrice,
} from "@/api";
import { toastError, Message } from "@/utils";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  settlement: {
    type: Object,
    default: () => ({}),
  },
  enterpriseId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["refresh"]);

const visible = ref(false);

// 服务类别
const serviceClasses = ref([]);
const activeCategory = ref("");
const isLoadingClasses = ref(false);

// 服务项目
const serviceItems = ref([]);
const isLoadingItems = ref(false);
const searchKeyword = ref("");

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const defaultPageSize = 10;
const total = ref(0);

// 获取服务类别
const getServiceClasses = async () => {
  try {
    isLoadingClasses.value = true;
    const res = await getServiceContentApi();
    if (res.data && res.data.data) {
      serviceClasses.value = res.data.data;
      // 默认选中第一个类别
      if (serviceClasses.value.length > 0 && !activeCategory.value) {
        activeCategory.value = serviceClasses.value[0].id;
        getServiceItems();
      }
    }
    isLoadingClasses.value = false;
  } catch (error) {
    toastError(error, "获取服务类别失败");
    isLoadingClasses.value = false;
  }
};

// 点击服务类别
const handleCategoryClick = (category) => {
  activeCategory.value = category.id;
  currentPage.value = 1; // 重置分页
  getServiceItems();
};

// 获取服务项目
const getServiceItems = async () => {
  try {
    if (!activeCategory.value) return;
    isLoadingItems.value = true;

    // 构建请求参数
    const params = {
      limit: pageSize.value,
      offset: (currentPage.value - 1) * pageSize.value,
      filters: `serviceClass=${activeCategory.value}`,
      maintenanceUnitId: props.enterpriseId,
      settlementId: props.settlement.id,
    };

    // 如果有搜索关键词，添加到过滤条件
    if (searchKeyword.value) {
      params.filters += `,serviceItem=${searchKeyword.value}`;
    }

    const res = await getMaintenanceServiceItemApi(params);

    // 确保返回的数据结构正确
    if (res.data && res.data.data) {
      // 处理返回的数据，添加结算价格和税率字段
      const items = res.data.data.rows || [];
      serviceItems.value = items.map((item) => {
        // 查找是否已有配置
        return {
          ...item,
          settlementPrice: item.settlementPrice || item.maxPrice,
          customTaxRate: item.customTaxRate || item.taxRate,
        };
      });
      total.value = res.data.data.pageElements?.totalElements || 0;
    } else {
      serviceItems.value = [];
      total.value = 0;
    }

    isLoadingItems.value = false;
  } catch (error) {
    toastError(error, "获取服务项目失败");
    isLoadingItems.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置分页
  getServiceItems();
};

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  getServiceItems();
};

// 当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  getServiceItems();
};

// 初始化数据
const initData = () => {
  // 重置搜索关键词
  searchKeyword.value = "";

  // 重置分页状态
  currentPage.value = 1;
  pageSize.value = defaultPageSize;
};

// 打开弹窗
const openDialog = () => {
  visible.value = true;
  nextTick(() => {
    initData();
    getServiceClasses();
  });
};

// 保存
const handleSave = async (row) => {
  row.editLoading = true;
  editMaintenanceEnterpriseSettlementPrice({
    id: props.settlement.id,
    unitId: props.enterpriseId,
    serviceClassId: row.serviceClass,
    serviceItemId: row._id,
    settlementPrice: row.settlementPrice,
    customTaxRate: row.customTaxRate,
  })
    .then((res) => {
      Message.success("保存成功");
      emit("refresh");
    })
    .catch((err) => {
      toastError(err, "保存失败");
    })
    .finally(() => {
      row.editLoading = false;
    });
};

defineExpose({
  openDialog,
});
</script>

<style lang="less" scoped>
.service-price-dialog {
  .service-price-layout {
    display: flex;
    gap: 20px;
    height: 700px;

    // 左侧服务类别面板
    .service-category-panel {
      width: 200px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 12px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;

        .category-item {
          padding: 10px 15px;
          cursor: pointer;
          border-bottom: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #409eff;
            color: #fff;
          }
        }
      }
    }

    // 右侧服务项目面板
    .service-items-panel {
      flex: 1;
      min-width: 0px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 12px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }

        .search-box {
          width: 250px;
        }
      }

      .panel-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .panel-tip {
          white-space: pre;
        }

        .pagination-container {
          padding: 15px;
          display: flex;
          justify-content: flex-end;
        }
      }

      .panel-footer {
        padding: 12px 15px;
        border-top: 1px solid #ebeef5;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
